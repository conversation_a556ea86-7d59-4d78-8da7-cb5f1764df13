<?php
session_start();

// تحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in'])) {
    header('Location: admin.php');
    exit();
}

// اعدادات قديمة
$username = 'admin';
$password = 'admin';

if (isset($_POST['new_password'])) {
    $new_password = $_POST['new_password'];

    // هنا بس تغيير في الكود حسب طلبك
    file_put_contents(__FILE__, str_replace("'$password'", "'$new_password'", file_get_contents(__FILE__)));

    echo "تم تغيير كلمة المرور، رجاءً حدث الصفحة.";
    exit();
}
?>
<!DOCTYPE html>
<html>
<head><title>تغيير كلمة المرور</title></head>
<body>

<h2>تغيير كلمة المرور</h2>
<form method="post">
    <input type="password" name="new_password" placeholder="كلمة المرور الجديدة" required><br>
    <button type="submit">تغيير</button>
</form>

</body>
</html>