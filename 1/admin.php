<?php
session_start();

// إعدادات تسجيل الدخول
$username = 'admin';
$password = 'admin';

// التحقق من تسجيل الدخول
if (isset($_POST['login'])) {
    if ($_POST['username'] === $username && $_POST['password'] === $password) {
        $_SESSION['logged_in'] = true;
        header('Location: admin.php');
        exit();
    } else {
        $error = "اسم المستخدم أو كلمة المرور خاطئة.";
    }
}

// تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in'])):
?>
<!DOCTYPE html>
<html>
<head><title>تسجيل الدخول</title></head>
<body>
<h2>تسجيل دخول</h2>
<form method="post">
    <input type="text" name="username" placeholder="اسم المستخدم" required><br>
    <input type="password" name="password" placeholder="كلمة المرور" required><br>
    <button type="submit" name="login">دخول</button>
</form>
<?php if (isset($error)) echo "<p style='color:red;'>$error</p>"; ?>
</body>
</html>

<?php
exit();
endif;

// تحميل السيرفرات
$servers = file_exists('servers.json') ? json_decode(file_get_contents('servers.json'), true) : [];

if (isset($_POST['add'])) {
    $servers[] = ['server_name' => $_POST['server_name'], 'url' => $_POST['url']];
    file_put_contents('servers.json', json_encode($servers, JSON_PRETTY_PRINT));
    header('Location: admin.php');
    exit();
}

if (isset($_GET['delete'])) {
    array_splice($servers, $_GET['delete'], 1);
    file_put_contents('servers.json', json_encode($servers, JSON_PRETTY_PRINT));
    header('Location: admin.php');
    exit();
}
?>
<!DOCTYPE html>
<html>
<head><title>لوحة التحكم</title></head>
<body>

<h2>السيرفرات</h2>
<a href="?logout=1">تسجيل خروج</a> | <a href="change_password.php">تغيير كلمة المرور</a><br><br>

<table border="1" cellpadding="5" cellspacing="0">
<tr><th>الاسم</th><th>الرابط</th><th>حذف</th></tr>
<?php foreach ($servers as $index => $server): ?>
<tr>
    <td><?php echo htmlspecialchars($server['server_name']); ?></td>
    <td><?php echo htmlspecialchars($server['url']); ?></td>
    <td><a href="?delete=<?php echo $index; ?>" onclick="return confirm('متأكد تبغى تحذفه؟')">حذف</a></td>
</tr>
<?php endforeach; ?>
</table>

<h3>إضافة سيرفر جديد</h3>
<form method="post">
    <input type="text" name="server_name" placeholder="اسم السيرفر" required><br>
    <input type="text" name="url" placeholder="رابط الهوست" required><br>
    <button type="submit" name="add">إضافة</button>
</form>

</body>
</html>