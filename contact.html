<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تواصل معنا - نوفا يمن</title>
  <meta name="description" content="تواصل مع فريق نوفا يمن للحصول على الدعم الفني أو الاستفسارات. نحن هنا لمساعدتك 24/7">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap"></noscript>

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- Nova Theme CSS -->
  <link rel="stylesheet" href="assets/css/nova-theme.css">
  <link rel="stylesheet" href="assets/css/nova-components.css">

  <style>
    /* تخصيصات خاصة بصفحة التواصل */

      /* المسافات */
      --spacing-xs: 8px;
      --spacing-sm: 12px;
      --spacing-md: 16px;
      --spacing-lg: 24px;
      --spacing-xl: 32px;
      --spacing-2xl: 48px;

      /* نصف الأقطار */
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --radius-xl: 20px;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: "Cairo", "Cairo-Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      line-height: 1.6;
      overflow-x: hidden;
      font-display: swap;

      /* تحسينات الأداء */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeSpeed;
      contain: layout style;
    }

    /* Header المحسن */
    header {
      position: fixed;
      top: 0;
      width: 100%;
      background: rgba(26, 29, 35, 0.95);
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      padding: 15px 0;
      z-index: 1000;
      box-shadow: var(--shadow-medium);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-bottom: 1px solid rgba(74, 144, 226, 0.1);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--spacing-lg);
    }

    .logo {
      font-size: 2rem;
      font-weight: 900;
      color: var(--primary-color);
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .logo:hover {
      color: #6afcff;
      transform: scale(1.05);
    }

    .nav-links {
      display: flex;
      gap: var(--spacing-xl);
    }

    .nav-links a {
      color: var(--text-secondary);
      text-decoration: none;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      font-weight: 500;
      position: relative;
      border: 1px solid transparent;
    }

    .nav-links a::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }

    .nav-links a:hover {
      color: var(--primary-color);
      background: rgba(74, 144, 226, 0.1);
      transform: translateY(-2px);
      border-color: rgba(74, 144, 226, 0.2);
    }

    .nav-links a:hover::before {
      width: 100%;
    }

    .nav-links a.active {
      color: var(--primary-color);
      background: rgba(74, 144, 226, 0.15);
      border-color: rgba(74, 144, 226, 0.3);
    }

    .nav-links a.active::before {
      width: 100%;
    }

    /* Main Content المحسن */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 var(--spacing-lg);
      margin-top: 100px; /* مساحة للـ fixed header */
    }

    .page-header {
      text-align: center;
      margin-bottom: var(--spacing-2xl);
      padding: var(--spacing-2xl) 0;
      background: linear-gradient(135deg, 
        rgba(45, 55, 72, 0.3), 
        rgba(36, 40, 49, 0.5)
      );
      border-radius: var(--radius-xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 50%, rgba(74, 144, 226, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .page-header h1 {
      font-size: clamp(2.5rem, 6vw, 3.5rem);
      font-weight: 900;
      background: linear-gradient(135deg, var(--primary-color), #6afcff, var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: var(--spacing-lg);
      position: relative;
      z-index: 2;
    }

    .page-header::after {
      content: '';
      position: absolute;
      bottom: 30px;
      left: 50%;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      border-radius: 2px;
      transform: translateX(-50%);
    }

    .page-header p {
      font-size: clamp(1.1rem, 3vw, 1.3rem);
      color: var(--text-secondary);
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.8;
      position: relative;
      z-index: 2;
    }

    /* Contact Grid */
    .contact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: var(--spacing-2xl);
      margin-bottom: var(--spacing-2xl);
    }

    .contact-info {
      background: var(--bg-card);
      padding: var(--spacing-2xl);
      border-radius: var(--radius-xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .contact-info::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(74, 144, 226, 0.1), 
        transparent
      );
      transition: left 0.6s ease;
    }

    .contact-info:hover::before {
      left: 100%;
    }

    .contact-info:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-heavy);
      border-color: rgba(74, 144, 226, 0.4);
      background: linear-gradient(135deg, 
        rgba(74, 144, 226, 0.1), 
        var(--bg-card)
      );
    }

    .contact-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
      padding: var(--spacing-lg);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(74, 144, 226, 0.1);
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .contact-item:hover {
      background: rgba(74, 144, 226, 0.1);
      border-color: rgba(74, 144, 226, 0.3);
      transform: translateX(5px);
    }

    .contact-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .contact-item:hover .contact-icon {
      background: linear-gradient(135deg, #6afcff, var(--accent-color));
      transform: scale(1.1);
      box-shadow: 0 0 20px rgba(106, 252, 255, 0.3);
    }

    .contact-details h3 {
      color: var(--text-primary);
      font-size: 1.2rem;
      margin-bottom: var(--spacing-xs);
      transition: color 0.3s ease;
    }

    .contact-item:hover .contact-details h3 {
      color: var(--primary-color);
    }

    .contact-details p {
      color: var(--text-secondary);
      font-size: 1rem;
    }

    .contact-details a {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .contact-details a:hover {
      color: #6afcff;
    }

    /* Contact Form */
    .contact-form {
      background: var(--bg-card);
      padding: var(--spacing-2xl);
      border-radius: var(--radius-xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .contact-form::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .contact-form:hover::before {
      left: 100%;
    }

    .contact-form:hover {
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-medium);
    }

    .form-group {
      margin-bottom: var(--spacing-lg);
      position: relative;
      z-index: 2;
    }

    .form-group label {
      display: block;
      color: var(--text-primary);
      font-weight: 500;
      margin-bottom: var(--spacing-sm);
      font-size: 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: var(--spacing-md);
      background: rgba(255, 255, 255, 0.05);
      border: 2px solid rgba(74, 144, 226, 0.2);
      border-radius: var(--radius-md);
      color: var(--text-primary);
      font-size: 1rem;
      transition: all 0.3s ease;
      font-family: inherit;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      background: rgba(74, 144, 226, 0.1);
      box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    }

    .form-group textarea {
      resize: vertical;
      min-height: 120px;
    }

    .submit-btn {
      width: 100%;
      padding: var(--spacing-md) var(--spacing-xl);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-sm);
      position: relative;
      overflow: hidden;
    }

    .submit-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .submit-btn:hover::before {
      left: 100%;
    }

    .submit-btn:hover {
      background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }

    .form-status {
      margin-top: var(--spacing-lg);
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      text-align: center;
      font-weight: 500;
    }

    .form-status.success {
      background: rgba(80, 200, 120, 0.1);
      color: var(--accent-color);
      border: 1px solid rgba(80, 200, 120, 0.3);
    }

    .form-status.error {
      background: rgba(220, 53, 69, 0.1);
      color: var(--error-color);
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    /* Quick Actions */
    .quick-actions {
      margin: var(--spacing-2xl) 0;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-top: var(--spacing-xl);
    }

    .action-card {
      background: var(--bg-card);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      border: 2px solid rgba(74, 144, 226, 0.2);
      text-decoration: none;
      color: var(--text-primary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      text-align: center;
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .action-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .action-card:hover::before {
      left: 100%;
    }

    .action-card:hover {
      transform: translateY(-5px);
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-medium);
    }

    .action-card.whatsapp:hover {
      border-color: #25d366;
      background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), var(--bg-card));
    }

    .action-card.telegram:hover {
      border-color: #0088cc;
      background: linear-gradient(135deg, rgba(0, 136, 204, 0.1), var(--bg-card));
    }

    .action-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: white;
      margin: 0 auto var(--spacing-lg);
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .action-card:hover .action-icon {
      transform: scale(1.1);
      box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
    }

    .action-card.whatsapp:hover .action-icon {
      background: linear-gradient(135deg, #25d366, #128c7e);
    }

    .action-card.telegram:hover .action-icon {
      background: linear-gradient(135deg, #0088cc, #005580);
    }

    .action-card h3 {
      font-size: 1.3rem;
      margin-bottom: var(--spacing-sm);
      color: var(--text-primary);
      transition: color 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .action-card:hover h3 {
      color: var(--primary-color);
    }

    .action-card p {
      color: var(--text-secondary);
      font-size: 0.9rem;
      position: relative;
      z-index: 2;
    }

    /* FAQ Section */
    .faq-section {
      margin: var(--spacing-2xl) 0;
    }

    .faq-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-lg);
      margin-top: var(--spacing-xl);
    }

    .faq-item {
      background: var(--bg-card);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .faq-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .faq-item:hover::before {
      left: 100%;
    }

    .faq-item:hover {
      transform: translateY(-3px);
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-light);
    }

    .faq-item h3 {
      color: var(--primary-color);
      font-size: 1.2rem;
      margin-bottom: var(--spacing-md);
      position: relative;
      z-index: 2;
      transition: color 0.3s ease;
    }

    .faq-item:hover h3 {
      color: #6afcff;
    }

    .faq-item p {
      color: var(--text-secondary);
      line-height: 1.6;
      position: relative;
      z-index: 2;
    }



    /* Responsive Design */
    @media (max-width: 768px) {
      .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .actions-grid,
      .faq-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .nav-links {
        flex-direction: column;
        gap: var(--spacing-md);
      }

      .container {
        margin-top: 140px;
      }
    }

    @media (max-width: 480px) {
      .contact-info,
      .contact-form,
      .action-card,
      .faq-item {
        padding: var(--spacing-lg);
      }

      .action-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <!-- Header المحسن -->
  <header class="nova-header">
    <div class="nova-header-content">
      <a href="index.html" class="nova-logo">Nova Yemen</a>

      <nav class="nova-nav">
        <a href="index.html" class="nova-nav-link">الرئيسية</a>
        <a href="services.html" class="nova-nav-link">الخدمات</a>
        <a href="about.html" class="nova-nav-link">من نحن</a>
        <a href="#" class="nova-nav-link active">تواصل معنا</a>
      </nav>

      <div class="nova-header-controls">
        <!-- زر تبديل الوضع الليلي/النهاري -->
        <button class="nova-theme-toggle" title="تبديل الوضع الليلي/النهاري">
          <i class="fas fa-sun nova-theme-toggle-icon sun"></i>
          <i class="fas fa-moon nova-theme-toggle-icon moon"></i>
        </button>

        <!-- زر قائمة الهامبرغر -->
        <button class="nova-mobile-menu-btn">
          <div class="nova-hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>
  </header>

  <!-- القائمة المحمولة -->
  <div class="nova-mobile-overlay"></div>
  <div class="nova-mobile-menu">
    <div class="nova-mobile-menu-header">
      <div class="nova-logo">Nova Yemen</div>
      <button class="nova-mobile-menu-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <nav class="nova-mobile-menu-nav">
      <a href="index.html" class="nova-nav-link">الرئيسية</a>
      <a href="services.html" class="nova-nav-link">الخدمات</a>
      <a href="about.html" class="nova-nav-link">من نحن</a>
      <a href="#" class="nova-nav-link active">تواصل معنا</a>
    </nav>
  </div>

  <div class="nova-container">
    <!-- Page Header -->
    <div class="page-header">
      <h1>📞 تواصل معنا</h1>
      <p>نحن هنا لمساعدتك في أي وقت. تواصل معنا عبر الطرق المختلفة المتاحة أدناه</p>
    </div>

    <!-- Contact Grid -->
    <div class="contact-grid">
      <!-- Contact Information -->
      <div class="contact-info">
        <h2 style="color: var(--primary-color); margin-bottom: var(--spacing-xl); font-size: 1.8rem; position: relative; z-index: 2;">معلومات التواصل</h2>
        
        <div class="contact-item">
          <div class="contact-icon">
            <i class="fab fa-whatsapp"></i>
          </div>
          <div class="contact-details">
            <h3>واتساب</h3>
            <p><a href="https://wa.me/967779600073">+967 779 600 073</a></p>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">
            <i class="fas fa-phone"></i>
          </div>
          <div class="contact-details">
            <h3>هاتف</h3>
            <p><a href="tel:+967779600073">+967 779 600 073</a></p>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">
            <i class="fab fa-telegram"></i>
          </div>
          <div class="contact-details">
            <h3>تيليجرام</h3>
            <p><a href="https://t.me/novayemen">@novayemen</a></p>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <div class="contact-details">
            <h3>البريد الإلكتروني</h3>
            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="contact-details">
            <h3>ساعات العمل</h3>
            <p>24/7 - متاحون دائماً</p>
          </div>
        </div>
      </div>

      <!-- Contact Form -->
      <div class="contact-form">
        <h2 style="color: var(--primary-color); margin-bottom: var(--spacing-xl); font-size: 1.8rem; position: relative; z-index: 2;">أرسل لنا رسالة</h2>

        <form id="contactForm" style="position: relative; z-index: 2;">
          <div class="form-group">
            <label for="name">الاسم الكامل</label>
            <input type="text" id="name" name="name" required>
          </div>

          <div class="form-group">
            <label for="email">البريد الإلكتروني</label>
            <input type="email" id="email" name="email" required>
          </div>

          <div class="form-group">
            <label for="phone">رقم الهاتف</label>
            <input type="tel" id="phone" name="phone" required>
          </div>

          <div class="form-group">
            <label for="subject">الموضوع</label>
            <select id="subject" name="subject" required>
              <option value="">اختر الموضوع</option>
              <option value="support">دعم فني</option>
              <option value="billing">استفسار عن الفواتير</option>
              <option value="service">استفسار عن الخدمات</option>
              <option value="complaint">شكوى</option>
              <option value="suggestion">اقتراح</option>
              <option value="other">أخرى</option>
            </select>
          </div>

          <div class="form-group">
            <label for="message">الرسالة</label>
            <textarea id="message" name="message" rows="5" required placeholder="اكتب رسالتك هنا..."></textarea>
          </div>

          <button type="submit" class="nova-btn nova-btn-primary nova-btn-lg" style="width: 100%;">
            <i class="fas fa-paper-plane"></i>
            إرسال الرسالة
          </button>
        </form>

        <div id="form-status" class="form-status" style="display: none;"></div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2 style="text-align: center; color: var(--primary-color); margin-bottom: var(--spacing-xl); font-size: 2rem;">طرق سريعة للتواصل</h2>

      <div class="actions-grid">
        <a href="https://wa.me/967779600073?text=مرحباً، أريد الاستفسار عن خدمات نوفا يمن" class="action-card whatsapp">
          <div class="action-icon">
            <i class="fab fa-whatsapp"></i>
          </div>
          <h3>واتساب</h3>
          <p>تواصل معنا فوراً عبر واتساب</p>
        </a>

        <a href="tel:+967779600073" class="action-card phone">
          <div class="action-icon">
            <i class="fas fa-phone"></i>
          </div>
          <h3>اتصال مباشر</h3>
          <p>اتصل بنا مباشرة للحصول على المساعدة</p>
        </a>

        <a href="https://t.me/novayemen" class="action-card telegram">
          <div class="action-icon">
            <i class="fab fa-telegram"></i>
          </div>
          <h3>تيليجرام</h3>
          <p>انضم إلى قناتنا على تيليجرام</p>
        </a>

        <a href="services.html" class="action-card services">
          <div class="action-icon">
            <i class="fas fa-tv"></i>
          </div>
          <h3>الخدمات</h3>
          <p>تصفح خدماتنا واشترك الآن</p>
        </a>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="faq-section">
      <h2 style="text-align: center; color: var(--primary-color); margin-bottom: var(--spacing-xl); font-size: 2rem;">الأسئلة الشائعة</h2>

      <div class="faq-grid">
        <div class="faq-item">
          <h3>كيف يمكنني الاشتراك؟</h3>
          <p>يمكنك الاشتراك عبر صفحة الخدمات أو التواصل معنا مباشرة عبر واتساب</p>
        </div>

        <div class="faq-item">
          <h3>ما هي طرق الدفع المتاحة؟</h3>
          <p>نقبل الدفع عبر التحويل البنكي، فودافون كاش، وطرق الدفع الإلكترونية الأخرى</p>
        </div>

        <div class="faq-item">
          <h3>هل يمكنني تجربة الخدمة مجاناً؟</h3>
          <p>نعم، نوفر تجربة مجانية لمدة 24 ساعة لجميع العملاء الجدد</p>
        </div>

        <div class="faq-item">
          <h3>ما هي الأجهزة المدعومة؟</h3>
          <p>ندعم جميع الأجهزة: أندرويد، آيفون، أجهزة التلفزيون الذكية، والكمبيوتر</p>
        </div>
      </div>
    </div>
  </div>

  <script>
    // كشف الأجهزة الضعيفة للتحسين
    const isLowEndDevice = navigator.hardwareConcurrency <= 2;
    const isSlowConnection = navigator.connection && navigator.connection.effectiveType &&
                           (navigator.connection.effectiveType === 'slow-2g' ||
                            navigator.connection.effectiveType === '2g');

    // دالة throttle للأداء
    function throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    }

    // Header scroll effect محسن
    const updateHeader = throttle(function() {
      const header = document.querySelector('header');
      if (window.scrollY > 100) {
        header.style.background = 'rgba(26, 29, 35, 0.98)';
        header.style.borderBottomColor = 'rgba(74, 144, 226, 0.3)';
      } else {
        header.style.background = 'rgba(26, 29, 35, 0.95)';
        header.style.borderBottomColor = 'rgba(74, 144, 226, 0.1)';
      }
    }, 16);

    window.addEventListener('scroll', updateHeader);

    // Contact Form Handler
    document.getElementById('contactForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(this);
      const formStatus = document.getElementById('form-status');

      // جمع البيانات
      const name = formData.get('name');
      const email = formData.get('email');
      const phone = formData.get('phone');
      const subject = formData.get('subject');
      const message = formData.get('message');

      // التحقق من البيانات
      if (!name || !email || !phone || !subject || !message) {
        showFormStatus('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
      }

      // إنشاء رسالة واتساب
      const whatsappMessage = `*رسالة جديدة من موقع نوفا يمن*%0A%0A` +
        `*الاسم:* ${name}%0A` +
        `*البريد الإلكتروني:* ${email}%0A` +
        `*رقم الهاتف:* ${phone}%0A` +
        `*الموضوع:* ${subject}%0A` +
        `*الرسالة:* ${message}`;

      // فتح واتساب
      window.open(`https://wa.me/967779600073?text=${whatsappMessage}`, '_blank');

      // إظهار رسالة نجاح
      showFormStatus('تم إرسال رسالتك بنجاح! سيتم التواصل معك قريباً', 'success');

      // إعادة تعيين النموذج
      this.reset();
    });

    function showFormStatus(message, type) {
      const formStatus = document.getElementById('form-status');
      formStatus.textContent = message;
      formStatus.className = `form-status ${type}`;
      formStatus.style.display = 'block';

      // إخفاء الرسالة بعد 5 ثوان
      setTimeout(() => {
        formStatus.style.display = 'none';
      }, 5000);
    }



    console.log('🚀 تم تحميل صفحة التواصل بنجاح مع التحسينات المتقدمة');
  </script>

  <!-- Nova Core JavaScript -->
  <script src="assets/js/nova-core.js"></script>
</body>
</html>
