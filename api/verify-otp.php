<?php
/**
 * API التحقق من كود OTP
 * Nova Yemen Trial Protection - Verify OTP API
 */

require_once '../config/trial_config.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'طريقة الطلب غير مدعومة');
}

// التحقق من Content-Type
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($contentType, 'application/json') !== false) {
    $input = json_decode(file_get_contents('php://input'), true);
} else {
    $input = $_POST;
}

// استخراج البيانات
$phone = $input['phone'] ?? '';
$otpCode = $input['otp'] ?? '';
$service = $input['service'] ?? '';
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

// تسجيل الطلب
logActivity('otp_verify_request', $phone, [
    'service' => $service,
    'otp_provided' => !empty($otpCode)
]);

// التحقق من صحة البيانات
if (empty($phone) || empty($otpCode)) {
    sendResponse(false, 'يرجى إدخال رقم الجوال وكود التحقق');
}

// تنظيف البيانات
$phone = cleanPhoneNumber($phone);
$otpCode = preg_replace('/[^0-9]/', '', $otpCode);

// التحقق من صحة الرقم
if (!validateYemeniPhone($phone)) {
    logActivity('otp_verify_invalid_phone', $phone, [], false);
    sendResponse(false, MESSAGES['phone_invalid']);
}

// التحقق من طول كود OTP
if (strlen($otpCode) !== 6) {
    logActivity('otp_verify_invalid_format', $phone, [], false);
    sendResponse(false, 'كود التحقق يجب أن يكون 6 أرقام');
}

// الاتصال بقاعدة البيانات
$pdo = getDBConnection();
if (!$pdo) {
    logActivity('otp_verify_db_error', $phone, [], false);
    sendResponse(false, MESSAGES['system_error']);
}

try {
    // البحث عن المستخدم وكود OTP
    $stmt = $pdo->prepare("
        SELECT id, name, otp_code, otp_expires_at, otp_attempts, is_verified, blocked_until
        FROM trial_users 
        WHERE phone = ?
    ");
    $stmt->execute([$phone]);
    $user = $stmt->fetch();
    
    if (!$user) {
        logActivity('otp_verify_user_not_found', $phone, [], false);
        sendResponse(false, 'لم يتم العثور على طلب تحقق لهذا الرقم');
    }
    
    // التحقق من حالة الحظر
    if ($user['blocked_until'] && new DateTime($user['blocked_until']) > new DateTime()) {
        logActivity('otp_verify_user_blocked', $phone, [], false);
        sendResponse(false, MESSAGES['too_many_attempts']);
    }
    
    // التحقق من انتهاء صلاحية الكود
    if (!$user['otp_expires_at'] || new DateTime($user['otp_expires_at']) < new DateTime()) {
        logActivity('otp_verify_expired', $phone, [], false);
        sendResponse(false, MESSAGES['otp_expired']);
    }
    
    // التحقق من صحة الكود
    if ($user['otp_code'] !== $otpCode) {
        // زيادة عدد المحاولات الخاطئة
        $newAttempts = $user['otp_attempts'] + 1;
        
        $stmt = $pdo->prepare("
            UPDATE trial_users 
            SET otp_attempts = ? 
            WHERE phone = ?
        ");
        $stmt->execute([$newAttempts, $phone]);
        
        // تسجيل المحاولة الخاطئة
        $stmt = $pdo->prepare("
            INSERT INTO otp_requests_log (phone, otp_code, action, ip_address, user_agent, success, error_message) 
            VALUES (?, ?, 'verify', ?, ?, FALSE, 'Invalid OTP code')
        ");
        $stmt->execute([
            $phone, 
            $otpCode, 
            $ip, 
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        logActivity('otp_verify_invalid_code', $phone, [
            'attempts' => $newAttempts,
            'max_attempts' => MAX_OTP_ATTEMPTS
        ], false);
        
        // التحقق من تجاوز عدد المحاولات
        if ($newAttempts >= MAX_OTP_ATTEMPTS) {
            $blockUntil = date('Y-m-d H:i:s', strtotime('+' . BLOCK_DURATION_HOURS . ' hours'));
            $stmt = $pdo->prepare("
                UPDATE trial_users 
                SET blocked_until = ?, otp_attempts = 0 
                WHERE phone = ?
            ");
            $stmt->execute([$blockUntil, $phone]);
            
            logActivity('otp_verify_user_blocked_max_attempts', $phone, [
                'block_until' => $blockUntil
            ], false);
            sendResponse(false, MESSAGES['too_many_attempts']);
        }
        
        $remainingAttempts = MAX_OTP_ATTEMPTS - $newAttempts;
        sendResponse(false, "كود التحقق غير صحيح. المحاولات المتبقية: {$remainingAttempts}");
    }
    
    // الكود صحيح - تحديث حالة التحقق
    $stmt = $pdo->prepare("
        UPDATE trial_users 
        SET is_verified = TRUE, otp_attempts = 0, otp_code = NULL, otp_expires_at = NULL
        WHERE phone = ?
    ");
    $stmt->execute([$phone]);
    
    // تسجيل التحقق الناجح
    $stmt = $pdo->prepare("
        INSERT INTO otp_requests_log (phone, otp_code, action, ip_address, user_agent, success) 
        VALUES (?, ?, 'verify', ?, ?, TRUE)
    ");
    $stmt->execute([
        $phone, 
        $otpCode, 
        $ip, 
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    logActivity('otp_verify_success', $phone, [
        'service' => $service,
        'user_id' => $user['id']
    ]);
    
    // إنشاء token للجلسة (اختياري)
    $sessionToken = bin2hex(random_bytes(32));
    
    sendResponse(true, 'تم التحقق بنجاح', [
        'user_id' => $user['id'],
        'name' => $user['name'],
        'service' => $service,
        'session_token' => $sessionToken,
        'can_create_trial' => true
    ]);
    
} catch (Exception $e) {
    error_log("OTP Verify Error: " . $e->getMessage());
    logActivity('otp_verify_exception', $phone, [
        'error' => $e->getMessage()
    ], false);
    sendResponse(false, MESSAGES['system_error']);
}
?>
