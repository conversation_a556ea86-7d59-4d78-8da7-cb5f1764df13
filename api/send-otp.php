<?php
/**
 * API إرسال كود التحقق OTP
 * Nova Yemen Trial Protection - Send OTP API
 */

require_once '../config/trial_config.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'طريقة الطلب غير مدعومة');
}

// التحقق من Content-Type
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($contentType, 'application/json') !== false) {
    $input = json_decode(file_get_contents('php://input'), true);
} else {
    $input = $_POST;
}

// استخراج البيانات
$phone = $input['phone'] ?? '';
$name = $input['name'] ?? '';
$service = $input['service'] ?? '';
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

// تسجيل الطلب
logActivity('otp_request', $phone, [
    'service' => $service,
    'name' => $name
]);

// التحقق من صحة البيانات
if (empty($phone)) {
    sendResponse(false, MESSAGES['phone_invalid']);
}

// تنظيف رقم الجوال
$phone = cleanPhoneNumber($phone);

// التحقق من صحة الرقم
if (!validateYemeniPhone($phone)) {
    logActivity('otp_request_invalid_phone', $phone, [], false);
    sendResponse(false, MESSAGES['phone_invalid']);
}

// التحقق من Rate Limiting
if (!checkRateLimit($ip)) {
    logActivity('otp_request_rate_limited', $phone, ['ip' => $ip], false);
    sendResponse(false, 'تم تجاوز عدد الطلبات المسموحة، يرجى المحاولة لاحقاً');
}

// التحقق من حالة الحظر
if (isBlocked($phone)) {
    logActivity('otp_request_blocked', $phone, [], false);
    sendResponse(false, MESSAGES['too_many_attempts']);
}

// الاتصال بقاعدة البيانات
$pdo = getDBConnection();
if (!$pdo) {
    logActivity('otp_request_db_error', $phone, [], false);
    sendResponse(false, MESSAGES['system_error']);
}

try {
    // التحقق من آخر طلب OTP
    $stmt = $pdo->prepare("
        SELECT otp_expires_at, otp_attempts, last_trial_date
        FROM trial_users 
        WHERE phone = ?
    ");
    $stmt->execute([$phone]);
    $user = $stmt->fetch();
    
    if ($user) {
        // التحقق من آخر حساب تجريبي
        if ($user['last_trial_date']) {
            $lastTrialDate = new DateTime($user['last_trial_date']);
            $now = new DateTime();
            $daysDiff = $now->diff($lastTrialDate)->days;
            
            if ($daysDiff < TRIAL_COOLDOWN_DAYS) {
                $remainingDays = TRIAL_COOLDOWN_DAYS - $daysDiff;
                logActivity('otp_request_trial_cooldown', $phone, [
                    'remaining_days' => $remainingDays
                ], false);
                sendResponse(false, "لقد أنشأت حساباً تجريبياً مؤخراً. يمكنك إنشاء حساب جديد بعد {$remainingDays} أيام");
            }
        }
        
        // التحقق من آخر OTP
        if ($user['otp_expires_at']) {
            $otpExpiry = new DateTime($user['otp_expires_at']);
            $now = new DateTime();
            
            if ($now < $otpExpiry) {
                $minutesLeft = $now->diff($otpExpiry)->i;
                if ($minutesLeft > (OTP_EXPIRY_MINUTES - OTP_RESEND_MINUTES)) {
                    logActivity('otp_request_too_soon', $phone, [
                        'minutes_left' => $minutesLeft
                    ], false);
                    sendResponse(false, MESSAGES['otp_resend_wait']);
                }
            }
        }
        
        // التحقق من عدد المحاولات
        if ($user['otp_attempts'] >= MAX_OTP_ATTEMPTS) {
            // حظر المستخدم
            $blockUntil = date('Y-m-d H:i:s', strtotime('+' . BLOCK_DURATION_HOURS . ' hours'));
            $stmt = $pdo->prepare("
                UPDATE trial_users 
                SET blocked_until = ?, otp_attempts = 0 
                WHERE phone = ?
            ");
            $stmt->execute([$blockUntil, $phone]);
            
            logActivity('otp_request_user_blocked', $phone, [
                'block_until' => $blockUntil
            ], false);
            sendResponse(false, MESSAGES['too_many_attempts']);
        }
    }
    
    // توليد كود OTP جديد
    $otpCode = generateOTP();
    $otpExpiry = date('Y-m-d H:i:s', strtotime('+' . OTP_EXPIRY_MINUTES . ' minutes'));
    
    // حفظ أو تحديث بيانات المستخدم
    if ($user) {
        $stmt = $pdo->prepare("
            UPDATE trial_users 
            SET otp_code = ?, otp_expires_at = ?, is_verified = FALSE, name = ?
            WHERE phone = ?
        ");
        $stmt->execute([$otpCode, $otpExpiry, $name, $phone]);
    } else {
        $stmt = $pdo->prepare("
            INSERT INTO trial_users (phone, name, otp_code, otp_expires_at) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$phone, $name, $otpCode, $otpExpiry]);
    }
    
    // تسجيل طلب OTP
    $stmt = $pdo->prepare("
        INSERT INTO otp_requests_log (phone, otp_code, action, ip_address, user_agent, success) 
        VALUES (?, ?, 'send', ?, ?, TRUE)
    ");
    $stmt->execute([
        $phone, 
        $otpCode, 
        $ip, 
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // إرسال OTP عبر واتساب
    $whatsappResult = sendOTPViaWhatsApp($phone, $otpCode, $service);
    
    if ($whatsappResult) {
        logActivity('otp_sent_success', $phone, [
            'service' => $service,
            'otp_expiry' => $otpExpiry
        ]);
        sendResponse(true, MESSAGES['otp_sent'], [
            'expires_in' => OTP_EXPIRY_MINUTES * 60 // بالثواني
        ]);
    } else {
        logActivity('otp_send_whatsapp_failed', $phone, [
            'service' => $service
        ], false);
        sendResponse(false, 'فشل في إرسال كود التحقق، يرجى المحاولة لاحقاً');
    }
    
} catch (Exception $e) {
    error_log("OTP Send Error: " . $e->getMessage());
    logActivity('otp_send_exception', $phone, [
        'error' => $e->getMessage()
    ], false);
    sendResponse(false, MESSAGES['system_error']);
}

/**
 * دالة إرسال OTP عبر واتساب
 */
function sendOTPViaWhatsApp($phone, $otpCode, $service) {
    $serviceName = $service === 'nova-plus' ? 'نوفا بلص' : 'نوفا';
    
    $message = "🔐 كود التحقق الخاص بك من نوفا يمن:\n\n";
    $message .= "الكود: *{$otpCode}*\n\n";
    $message .= "📺 الخدمة: {$serviceName}\n";
    $message .= "⏰ صالح لمدة " . OTP_EXPIRY_MINUTES . " دقيقة فقط\n\n";
    $message .= "❌ لا تشارك هذا الكود مع أحد\n";
    $message .= "🔒 هذا الكود للاستخدام الشخصي فقط";
    
    $data = [
        'phone' => '967' . $phone,
        'message' => $message,
        'service' => $service
    ];
    
    // إرسال الطلب إلى VPS
    $ch = curl_init(WHATSAPP_API_URL . '/send-otp');
    curl_setopt_array($ch, [
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200 && $response) {
        $result = json_decode($response, true);
        return $result['success'] ?? false;
    }
    
    return false;
}
?>
