<?php
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type");

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    $settings = new ServiceSettings($db);

    $service = $_GET['service'] ?? 'nova';
    
    // جلب جميع إعدادات الخدمة
    $serviceSettings = $settings->getServiceSettings($service);
    
    // تحويل النتائج إلى مصفوفة مفاتيح => قيم لسهولة الاستخدام
    $settingsArray = [];
    foreach ($serviceSettings as $setting) {
        $settingsArray[$setting['setting_key']] = $setting['setting_value'];
    }
    
    // إضافة القيم الافتراضية إذا لم تكن موجودة
    $defaults = [
        'server_url' => $service === 'nova' ? 'http://nvpro.tv:80' : 'http://tayaar.site:2095',
        'app_download_url' => 'https://linkjar.co/NOVA_YEMEN',
        'trial_duration' => $service === 'nova' ? '12' : '24',
        'whatsapp_number' => '967779600073',
        'telegram_support' => '967730022822'
    ];
    
    foreach ($defaults as $key => $defaultValue) {
        if (!isset($settingsArray[$key])) {
            $settingsArray[$key] = $defaultValue;
        }
    }
    
    echo json_encode([
        'success' => true,
        'service' => $service,
        'settings' => $settingsArray
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>
