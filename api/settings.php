<?php
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

// بدء الجلسة للتحقق من تسجيل الدخول
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit();
}

$database = new Database();
$db = $database->getConnection();
$settings = new ServiceSettings($db);

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['service'])) {
                // جلب إعدادات خدمة معينة
                $service_name = $_GET['service'];
                $result = $settings->getServiceSettings($service_name);
                echo json_encode(['success' => true, 'data' => $result]);
            } elseif (isset($_GET['services'])) {
                // جلب جميع الخدمات
                $result = $settings->getAllServices();
                echo json_encode(['success' => true, 'data' => $result]);
            } elseif (isset($_GET['setting'])) {
                // جلب إعداد واحد
                $service = $_GET['service'] ?? '';
                $key = $_GET['setting'];
                $result = $settings->getSetting($service, $key);
                echo json_encode(['success' => true, 'data' => $result]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'معاملات غير صحيحة']);
            }
            break;

        case 'POST':
            // إضافة إعداد جديد
            if (!isset($input['service_name']) || !isset($input['setting_key']) || 
                !isset($input['setting_value']) || !isset($input['setting_label'])) {
                http_response_code(400);
                echo json_encode(['error' => 'بيانات ناقصة']);
                break;
            }

            $result = $settings->addSetting(
                $input['service_name'],
                $input['setting_key'],
                $input['setting_value'],
                $input['setting_label']
            );

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة الإعداد بنجاح']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'فشل في إضافة الإعداد']);
            }
            break;

        case 'PUT':
            // تحديث إعداد
            if (!isset($input['service_name']) || !isset($input['setting_key']) || !isset($input['setting_value'])) {
                http_response_code(400);
                echo json_encode(['error' => 'بيانات ناقصة']);
                break;
            }

            $result = $settings->updateSetting(
                $input['service_name'],
                $input['setting_key'],
                $input['setting_value']
            );

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث الإعداد بنجاح']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'فشل في تحديث الإعداد']);
            }
            break;

        case 'DELETE':
            // حذف إعداد
            if (!isset($input['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'معرف الإعداد مطلوب']);
                break;
            }

            $result = $settings->deleteSetting($input['id']);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم حذف الإعداد بنجاح']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'فشل في حذف الإعداد']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}
?>
