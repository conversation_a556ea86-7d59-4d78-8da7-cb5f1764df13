<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Nova Yemen | الصفحة الرئيسية - نوفا يمن</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

  <!-- Nova Theme CSS -->
  <link rel="stylesheet" href="assets/css/nova-theme.css">
  <link rel="stylesheet" href="assets/css/nova-components.css">

  <style>
    /* استخدام متغيرات CSS من nova-theme.css */
    :root {
      /* تم نقل جميع المتغيرات إلى nova-theme.css */

      /* المسافات */
      --spacing-xs: 8px;
      --spacing-sm: 12px;
      --spacing-md: 16px;
      --spacing-lg: 24px;
      --spacing-xl: 32px;
      --spacing-2xl: 48px;

      /* نصف الأقطار */
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --radius-xl: 20px;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: "Cairo", "Cairo-Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      line-height: 1.6;
      overflow-x: hidden;
      font-display: swap;

      /* تحسينات الأداء */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeSpeed;
      contain: layout style;
    }

    /* تخصيصات خاصة بالصفحة الرئيسية */





    /* Hero Section العصري */
    .hero {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      position: relative;
      background: linear-gradient(135deg,
        var(--bg-primary) 0%,
        var(--bg-secondary) 50%,
        var(--bg-tertiary) 100%
      );
      overflow: hidden;
    }

    /* خلفية متحركة للـ Hero */
    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
      animation: heroBackground 20s ease-in-out infinite;
      pointer-events: none;
    }

    @keyframes heroBackground {
      0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.7;
      }
      50% {
        transform: scale(1.1) rotate(2deg);
        opacity: 1;
      }
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 20%, rgba(74, 144, 226, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 70% 80%, rgba(80, 200, 120, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%236afcff" stop-opacity="0.1"/><stop offset="100%" stop-color="%236afcff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
      background-size: cover;
      opacity: 0.3;
    }

    .hero-content {
      max-width: 900px;
      padding: 0 var(--spacing-lg);
      position: relative;
      z-index: 2;
      animation: heroFadeIn 1s ease-out;
    }

    @keyframes heroFadeIn {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .hero h1 {
      font-size: clamp(2.5rem, 8vw, 5rem);
      font-weight: 900;
      margin-bottom: var(--spacing-xl);
      background: linear-gradient(135deg,
        var(--primary-color) 0%,
        var(--secondary-color) 50%,
        var(--accent-color) 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      background-size: 200% 200%;
      animation: gradientShift 4s ease-in-out infinite, textGlow 3s ease-in-out infinite alternate;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }

    @keyframes gradientShift {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    @keyframes textGlow {
      from {
        filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.3));
      }
      to {
        filter: drop-shadow(0 0 40px rgba(99, 102, 241, 0.6));
      }
    }

    .hero p {
      font-size: clamp(1.1rem, 3vw, 1.4rem);
      margin-bottom: var(--spacing-2xl);
      color: var(--text-secondary);
      line-height: 1.8;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-buttons {
      display: flex;
      gap: var(--spacing-lg);
      justify-content: center;
      flex-wrap: wrap;
      margin-top: var(--spacing-lg);
    }

    .btn {
      display: inline-block;
      padding: var(--spacing-md) var(--spacing-xl);
      border-radius: var(--radius-lg);
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      min-width: 180px;
      text-align: center;
      border: none;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .btn:hover::before {
      left: 100%;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      box-shadow: var(--shadow-light);
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
      transform: translateY(-3px);
      box-shadow: var(--shadow-medium);
    }

    .btn-secondary {
      background: transparent;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .btn-secondary:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-3px);
      box-shadow: var(--shadow-light);
    }

    /* Features Section المحسن */
    .features {
      padding: var(--spacing-2xl) 0;
      background: linear-gradient(135deg,
        rgba(45, 55, 72, 0.3),
        rgba(36, 40, 49, 0.5)
      );
      position: relative;
    }

    .features::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(74, 144, 226, 0.05) 0%, transparent 50%);
      pointer-events: none;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 var(--spacing-lg);
      position: relative;
      z-index: 2;
    }

    .section-title {
      text-align: center;
      margin-bottom: var(--spacing-2xl);
      position: relative;
    }

    .section-title h2 {
      font-size: clamp(2rem, 5vw, 3rem);
      font-weight: 900;
      background: linear-gradient(135deg, var(--primary-color), #6afcff, var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: var(--spacing-md);
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      border-radius: 2px;
      transform: translateX(-50%);
    }

    .section-title p {
      font-size: clamp(1rem, 2.5vw, 1.2rem);
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: var(--spacing-xl);
      margin-top: var(--spacing-2xl);
    }

    .feature-card {
      background: var(--bg-card);
      padding: var(--spacing-xl) var(--spacing-lg);
      border-radius: var(--radius-xl);
      text-align: center;
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .feature-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .feature-card:hover::before {
      left: 100%;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: var(--shadow-heavy);
      border-color: rgba(74, 144, 226, 0.4);
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1),
        var(--bg-card)
      );
    }

    .feature-icon {
      font-size: clamp(3rem, 6vw, 4rem);
      color: var(--primary-color);
      margin-bottom: var(--spacing-lg);
      display: block;
      transition: all 0.3s ease;
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
    }

    .feature-card:hover .feature-icon {
      color: #6afcff;
      transform: scale(1.1);
      text-shadow: 0 0 30px rgba(106, 252, 255, 0.5);
    }

    .feature-card h3 {
      font-size: clamp(1.2rem, 3vw, 1.5rem);
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
      transition: color 0.3s ease;
    }

    .feature-card:hover h3 {
      color: var(--primary-color);
    }

    .feature-card p {
      color: var(--text-secondary);
      line-height: 1.7;
      font-size: clamp(0.9rem, 2vw, 1rem);
    }

    /* Services Section المحسن */
    .services {
      padding: var(--spacing-2xl) 0;
      background: linear-gradient(135deg,
        rgba(36, 40, 49, 0.3),
        rgba(45, 55, 72, 0.2)
      );
      position: relative;
    }

    .services::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(80, 200, 120, 0.05) 0%, transparent 50%);
      pointer-events: none;
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-xl);
      margin-top: var(--spacing-2xl);
    }

    .service-card {
      background: linear-gradient(135deg, var(--bg-card), rgba(45, 55, 72, 0.9));
      padding: var(--spacing-2xl) var(--spacing-xl);
      border-radius: var(--radius-xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      position: relative;
    }

    .service-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .service-card:hover::before {
      left: 100%;
    }

    .service-card:hover {
      transform: translateY(-10px);
      box-shadow: var(--shadow-heavy);
      border-color: rgba(74, 144, 226, 0.4);
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1),
        var(--bg-card)
      );
    }

    .service-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
      position: relative;
      z-index: 2;
    }

    .service-header h3 {
      font-size: clamp(1.5rem, 4vw, 2rem);
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: var(--spacing-sm);
      transition: color 0.3s ease;
    }

    .service-card:hover .service-header h3 {
      color: #6afcff;
    }

    .service-price {
      font-size: clamp(1rem, 2.5vw, 1.1rem);
      color: var(--text-secondary);
      margin-bottom: var(--spacing-lg);
    }

    .service-features {
      list-style: none;
      margin-bottom: var(--spacing-xl);
      position: relative;
      z-index: 2;
    }

    .service-features li {
      padding: var(--spacing-sm) 0;
      color: var(--text-primary);
      position: relative;
      padding-right: var(--spacing-xl);
      font-size: clamp(0.9rem, 2vw, 1rem);
      transition: color 0.3s ease;
    }

    .service-features li::before {
      content: '✓';
      position: absolute;
      right: 0;
      color: var(--accent-color);
      font-weight: bold;
      font-size: 1.2em;
      text-shadow: 0 0 10px rgba(80, 200, 120, 0.5);
    }

    .service-card:hover .service-features li {
      color: var(--text-secondary);
    }

    .service-btn {
      width: 100%;
      text-align: center;
      padding: var(--spacing-md);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      text-decoration: none;
      border-radius: var(--radius-lg);
      font-weight: 600;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: block;
      position: relative;
      overflow: hidden;
      z-index: 2;
    }

    .service-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .service-btn:hover::before {
      left: 100%;
    }

    .service-btn:hover {
      transform: translateY(-2px);
      background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
      box-shadow: var(--shadow-medium);
    }

    /* Contact Section المحسن */
    .contact {
      padding: var(--spacing-2xl) 0;
      background: linear-gradient(135deg,
        rgba(45, 55, 72, 0.3),
        rgba(36, 40, 49, 0.5)
      );
      position: relative;
    }

    .contact::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 50%, rgba(74, 144, 226, 0.05) 0%, transparent 50%);
      pointer-events: none;
    }

    .contact-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-2xl);
      margin-top: var(--spacing-2xl);
      position: relative;
      z-index: 2;
    }

    .contact-info {
      text-align: center;
    }

    .contact-item {
      background: var(--bg-card);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      margin-bottom: var(--spacing-lg);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .contact-item:hover {
      transform: translateY(-5px);
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-medium);
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1),
        var(--bg-card)
      );
    }

    .contact-item i {
      font-size: clamp(2rem, 5vw, 2.5rem);
      color: var(--primary-color);
      margin-bottom: var(--spacing-md);
      display: block;
      transition: all 0.3s ease;
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
    }

    .contact-item:hover i {
      color: #6afcff;
      transform: scale(1.1);
      text-shadow: 0 0 30px rgba(106, 252, 255, 0.5);
    }

    .contact-item h4 {
      font-size: clamp(1.1rem, 3vw, 1.3rem);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
      transition: color 0.3s ease;
    }

    .contact-item:hover h4 {
      color: var(--primary-color);
    }

    .contact-item p {
      color: var(--text-secondary);
      line-height: 1.6;
    }

    .contact-item a {
      color: var(--primary-color);
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .contact-item a:hover {
      color: #6afcff;
      text-decoration: underline;
    }

    /* Footer العصري */
    footer {
      background: linear-gradient(135deg,
        var(--bg-secondary),
        var(--bg-tertiary)
      );
      padding: var(--spacing-2xl) 0 var(--spacing-xl);
      text-align: center;
      border-top: var(--border-glass);
      position: relative;
      margin-top: var(--spacing-2xl);
    }

    footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 0%, rgba(74, 144, 226, 0.05) 0%, transparent 50%);
      pointer-events: none;
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 var(--spacing-lg);
      position: relative;
      z-index: 2;
    }

    .social-links {
      margin: var(--spacing-xl) 0;
    }

    .social-links a {
      display: inline-block;
      width: 56px;
      height: 56px;
      background: var(--bg-card);
      color: var(--primary-color);
      border-radius: 50%;
      line-height: 56px;
      margin: 0 var(--spacing-sm);
      font-size: 1.2rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: var(--border-secondary);
      text-decoration: none;
    }

    .social-links a:hover {
      background: var(--primary-color);
      color: var(--text-inverse);
      transform: translateY(-4px) scale(1.1);
      box-shadow: var(--shadow-colored);
      border-color: var(--primary-color);
    }

    /* ألوان مخصصة للشبكات الاجتماعية */
    .social-links a[href*="facebook"]:hover {
      background: #1877f2;
      border-color: #1877f2;
    }

    .social-links a[href*="telegram"]:hover {
      background: #0088cc;
      border-color: #0088cc;
    }

    .social-links a[href*="whatsapp"]:hover {
      background: #25d366;
      border-color: #25d366;
    }

    .social-links a[href*="instagram"]:hover {
      background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
      border-color: #e6683c;
    }

    .social-links a[href*="youtube"]:hover {
      background: #ff0000;
      border-color: #ff0000;
    }

    .copyright {
      color: var(--text-muted);
      font-size: clamp(0.8rem, 2vw, 0.9rem);
      margin-top: var(--spacing-xl);
      padding-top: var(--spacing-xl);
      border-top: 1px solid rgba(74, 144, 226, 0.1);
      line-height: 1.6;
    }

    /* تصميم متجاوب محسن */
    @media (max-width: 768px) {
      .nova-nav {
        display: none;
      }

      .nova-mobile-menu-btn {
        display: flex;
      }

      .hero {
        padding: var(--spacing-2xl) var(--spacing-md);
        min-height: 90vh;
      }

      .hero h1 {
        font-size: clamp(2rem, 8vw, 3.5rem);
        margin-bottom: var(--spacing-lg);
      }

      .hero p {
        font-size: clamp(1rem, 4vw, 1.2rem);
        margin-bottom: var(--spacing-xl);
      }

      .nova-btn-xl {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
        min-height: 48px;
      }

      .features-grid,
      .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .social-links a {
        width: 48px;
        height: 48px;
        line-height: 48px;
        font-size: 1.1rem;
        margin: 0 var(--spacing-xs);
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 0 var(--spacing-md);
      }

      .feature-card,
      .service-card {
        padding: var(--spacing-lg);
      }

      .contact-item {
        padding: var(--spacing-lg);
      }
    }

    /* تحسينات الأداء */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }

    /* تحسينات للطباعة */
    @media print {
      .hero,
      .features,
      .services,
      .contact {
        page-break-inside: avoid;
      }

      .social-links {
        display: none;
      }
    }

    @media (max-width: 480px) {
      .hero h1 {
        font-size: 2rem;
      }

      .feature-card,
      .service-card {
        padding: 30px 20px;
      }

      .btn {
        padding: 12px 25px;
        font-size: 1rem;
      }
    }

    /* Animations */
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.6s ease;
    }

    .fade-in.visible {
      opacity: 1;
      transform: translateY(0);
    }

    /* Floating Animation */
    .floating {
      animation: floating 3s ease-in-out infinite;
    }

    @keyframes floating {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
  </style>
</head>
<body>
  <!-- Header المحسن -->
  <header class="nova-header">
    <div class="nova-header-content">
      <a href="#" class="nova-logo">Nova Yemen</a>

      <nav class="nova-nav">
        <a href="#home" class="nova-nav-link active">الرئيسية</a>
        <a href="services.html" class="nova-nav-link">الخدمات</a>
        <a href="about.html" class="nova-nav-link">من نحن</a>
        <a href="#features" class="nova-nav-link">المميزات</a>
        <a href="#contact" class="nova-nav-link">تواصل معنا</a>
      </nav>

      <div class="nova-header-controls">
        <!-- زر تبديل الوضع الليلي/النهاري -->
        <button class="nova-theme-toggle" title="تبديل الوضع الليلي/النهاري">
          <i class="fas fa-sun nova-theme-toggle-icon sun"></i>
          <i class="fas fa-moon nova-theme-toggle-icon moon"></i>
        </button>

        <!-- زر قائمة الهامبرغر -->
        <button class="nova-mobile-menu-btn">
          <div class="nova-hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>
  </header>

  <!-- القائمة المحمولة -->
  <div class="nova-mobile-overlay"></div>
  <div class="nova-mobile-menu">
    <div class="nova-mobile-menu-header">
      <div class="nova-logo">Nova Yemen</div>
      <button class="nova-mobile-menu-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <nav class="nova-mobile-menu-nav">
      <a href="#home" class="nova-nav-link active">الرئيسية</a>
      <a href="services.html" class="nova-nav-link">الخدمات</a>
      <a href="about.html" class="nova-nav-link">من نحن</a>
      <a href="#features" class="nova-nav-link">المميزات</a>
      <a href="#contact" class="nova-nav-link">تواصل معنا</a>
    </nav>
  </div>

  <!-- Hero Section -->
  <section id="home" class="hero">
    <div class="hero-content">
      <h1 class="floating">نوفا يمن</h1>
      <p>أفضل خدمات البث التلفزيوني في اليمن<br>
      استمتع بمشاهدة آلاف القنوات العربية والعالمية بجودة عالية</p>
      <div class="nova-flex nova-justify-center nova-gap-lg nova-flex-wrap nova-mt-xl">
        <a href="services.html" class="nova-btn nova-btn-primary nova-btn-xl">
          <i class="fas fa-rocket"></i> ابدأ الآن
        </a>
        <a href="#features" class="nova-btn nova-btn-outline-primary nova-btn-xl">
          <i class="fas fa-info-circle"></i> اعرف المزيد
        </a>
        <button onclick="window.openContactModal()" class="nova-btn nova-btn-success nova-btn-xl">
          <i class="fas fa-phone"></i> تواصل معنا
        </button>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="features">
    <div class="container">
      <div class="section-title fade-in">
        <h2>لماذا نوفا يمن؟</h2>
        <p>نقدم لك أفضل تجربة مشاهدة بمميزات لا تُضاهى</p>
      </div>

      <div class="nova-grid nova-grid-auto-md nova-gap-xl nova-mt-2xl">
        <div class="feature-card fade-in">
          <i class="fas fa-tv feature-icon"></i>
          <h3>جودة عالية</h3>
          <p>استمتع بمشاهدة القنوات بجودة HD و 4K مع صوت نقي وصورة واضحة</p>
        </div>

        <div class="feature-card fade-in">
          <i class="fas fa-globe feature-icon"></i>
          <h3>قنوات متنوعة</h3>
          <p>آلاف القنوات العربية والعالمية في جميع المجالات: رياضة، أفلام، أخبار، وأطفال</p>
        </div>

        <div class="feature-card fade-in">
          <i class="fas fa-mobile-alt feature-icon"></i>
          <h3>متعدد المنصات</h3>
          <p>شاهد على جميع أجهزتك: الهاتف، التابلت، الكمبيوتر، والشاشات الذكية</p>
        </div>

        <div class="feature-card fade-in">
          <i class="fas fa-headset feature-icon"></i>
          <h3>دعم فني 24/7</h3>
          <p>فريق الدعم الفني متاح على مدار الساعة لمساعدتك في أي وقت</p>
        </div>

        <div class="feature-card fade-in">
          <i class="fas fa-shield-alt feature-icon"></i>
          <h3>أمان وموثوقية</h3>
          <p>خدمة آمنة ومستقرة مع ضمان عدم انقطاع البث</p>
        </div>

        <div class="feature-card fade-in">
          <i class="fas fa-dollar-sign feature-icon"></i>
          <h3>أسعار تنافسية</h3>
          <p>أفضل الأسعار في السوق مع باقات مرنة تناسب جميع الاحتياجات</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section class="services">
    <div class="container">
      <div class="section-title fade-in">
        <h2>خدماتنا</h2>
        <p>اختر الباقة التي تناسبك</p>
      </div>

      <div class="nova-grid nova-grid-auto-lg nova-gap-xl nova-mt-2xl">
        <div class="service-card fade-in">
          <div class="service-header">
            <h3>نوفا العادي</h3>
            <div class="service-price">ابتداءً من 4300 ريال يمني</div>
          </div>
          <ul class="service-features">
            <li>قنوات رياضية تناسب كل سرعات الأنترنت</li>
            <li>بث مستقر دون تقطيع</li>
            <li>جودات منخفضه تناسب الانترنت الضعيف</li>
            <li>جميع الافلام والمسلسلات</li>
            <li>تحديث يومي للحلقات الجديده والافلام</li>
            <li>جودة اقل استهلاك اقل </li>
            <li>تجربة مجانية 12 ساعه قبل الاشتراك</li>
            <li>يدعم الانترنت الضعيف ١ميجا</li>
          </ul>
          <a href="services.html#nova" class="nova-btn nova-btn-primary nova-btn-lg" style="width: 100%;">
            <i class="fas fa-shopping-cart"></i> اطلب الآن
          </a>
        </div>

        <div class="service-card fade-in">
          <div class="service-header">
            <h3>نوفا بلص</h3>
            <div class="service-price">ابتداءً من 4300 ريال يمني</div>
          </div>
          <ul class="service-features">
            <li>جميع القنوات الرياضية بجودات اعلى</li>
            <li>بث مستقر دون تقطيع</li>
            <li>جودات عالية لاتناسب الانترنت الضعيف</li>
            <li>جميع الافلام والمسلسلات القديمة والحديثة</li>
            <li>جودات أعلى 4k</li>
            <li>تجربة مجانية24 ساعة قبل الاشتراك</li>
            <li>لايدعم الانترنت الضعيف يفضل ٣ميجا وانترنت مستقر</li>
          </ul>
          <a href="services.html#nova-plus" class="nova-btn nova-btn-success nova-btn-lg" style="width: 100%;">
            <i class="fas fa-crown"></i> اطلب الآن
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="section-title fade-in">
        <h2>تواصل معنا</h2>
        <p>نحن هنا لخدمتك في أي وقت</p>
      </div>

      <div class="contact-content">
        <div class="contact-info fade-in">
          <div class="contact-item">
            <i class="fab fa-whatsapp"></i>
            <h4>واتساب</h4>
            <p><a href="https://wa.me/967779600073">967779600073+</a></p>
          </div>

          <div class="contact-item">
            <i class="fab fa-telegram"></i>
            <h4>تليجرام</h4>
            <p><a href="#">@novayemen</a></p>
          </div>

          <div class="contact-item">
            <i class="fas fa-globe"></i>
            <h4>الموقع الإلكتروني</h4>
            <p><a href="https://novayemen.com">novayemen.com</a></p>
          </div>
        </div>

        <div class="contact-info fade-in">
          <div class="contact-item">
            <i class="fas fa-clock"></i>
            <h4>ساعات العمل</h4>
            <p>متاح 24 ساعة طوال الأسبوع</p>
          </div>

          <div class="contact-item">
            <i class="fas fa-download"></i>
            <h4>تحميل التطبيق</h4>
            <p><a href="https://linkjar.co/NOVA_YEMEN" target="_blank">رابط التحميل</a></p>
          </div>

          <div class="contact-item">
            <i class="fas fa-star"></i>
            <h4>تقييم العملاء</h4>
            <p>⭐⭐⭐⭐⭐ (4.8/5)</p>
          </div>
        </div>
      </div>

      <!-- زر التواصل السريع -->
      <div class="nova-text-center nova-mt-2xl">
        <button data-contact-modal class="nova-btn nova-btn-primary nova-btn-xl">
          <i class="fas fa-phone"></i> تواصل معنا الآن
        </button>
        <p class="nova-text-secondary nova-mt-md">اختر طريقة التواصل المناسبة لك</p>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <div class="logo" style="font-size: 1.5rem; margin-bottom: 20px;">Nova Yemen</div>

      <div class="social-links">
        <a href="#"><i class="fab fa-facebook"></i></a>
        <a href="#"><i class="fab fa-telegram"></i></a>
        <a href="#"><i class="fab fa-whatsapp"></i></a>
        <a href="#"><i class="fab fa-instagram"></i></a>
        <a href="#"><i class="fab fa-youtube"></i></a>
      </div>

      <div class="copyright">
        <p>جميع الحقوق محفوظة &copy; <span id="year">2025</span> نوفا يمن</p>
        <p>أفضل خدمات البث التلفزيوني في اليمن</p>
      </div>
    </div>
  </footer>

  <script>
    // تحديث السنة الحالية
    const yearElement = document.getElementById('year');
    if (yearElement) {
      yearElement.textContent = new Date().getFullYear();
    }

    console.log('🚀 تم تحميل الصفحة الرئيسية بنجاح مع النظام المحسن');
  </script>

  <!-- Nova Core JavaScript -->
  <script src="assets/js/nova-core.js"></script>
</body>
</html>
