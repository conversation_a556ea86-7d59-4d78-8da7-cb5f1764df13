<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <meta name="description" content="خدمات نوفا يمن - اشتراكات IPTV عالية الجودة مع تجربة مجانية">
  <meta name="robots" content="index, follow">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="theme-color" content="#4a90e2">

  <!-- تحسينات الأداء -->
  <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
  <meta name="format-detection" content="telephone=no">
  <meta name="msapplication-tap-highlight" content="no">

  <title>Nova Yemen | خدمات نوفا يمن - اشتراكات IPTV</title>
  <!-- تحميل محسن للخطوط والأيقونات -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap"></noscript>

  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

  <!-- Nova Theme CSS -->
  <link rel="stylesheet" href="assets/css/nova-theme.css">
  <link rel="stylesheet" href="assets/css/nova-components.css">

  <!-- خط احتياطي محلي للتحميل السريع -->
  <style>
    @font-face {
      font-family: 'Cairo-Fallback';
      src: local('Arial'), local('Helvetica'), local('sans-serif');
      font-display: swap;
    }
  </style>
  <style>
    :root {
      /* نظام ألوان مريح للعين */
      --primary-color: #4a90e2;
      --primary-dark: #357abd;
      --secondary-color: #7b68ee;
      --accent-color: #50c878;
      --success-color: #28a745;
      --warning-color: #ffc107;
      --error-color: #dc3545;

      /* ألوان الخلفية الهادئة */
      --bg-primary: #1a1d23;
      --bg-secondary: #242831;
      --bg-tertiary: #2d3748;
      --bg-card: rgba(36, 40, 49, 0.95);
      --bg-glass: rgba(45, 55, 72, 0.9);

      /* ألوان النص المريحة */
      --text-primary: #f7fafc;
      --text-secondary: #cbd5e0;
      --text-muted: #a0aec0;

      /* الظلال والحدود الناعمة */
      --shadow-light: 0 4px 20px rgba(74, 144, 226, 0.08);
      --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.25);
      --shadow-heavy: 0 12px 40px rgba(0, 0, 0, 0.35);
      --border-glass: 1px solid rgba(74, 144, 226, 0.15);

      /* المسافات */
      --spacing-xs: 8px;
      --spacing-sm: 12px;
      --spacing-md: 16px;
      --spacing-lg: 24px;
      --spacing-xl: 32px;
      --spacing-2xl: 48px;

      /* نصف الأقطار */
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --radius-xl: 20px;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: "Cairo", "Cairo-Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
      padding: 0;
      line-height: 1.6;
      overflow-x: hidden;
      font-display: swap;

      /* تحسينات الأداء */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeSpeed;
      contain: layout style;
    }

    /* خلفية ثابتة محسنة للأداء */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 80%, rgba(74, 144, 226, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(123, 104, 238, 0.02) 0%, transparent 50%);
      z-index: -1;
    }
    /* Header محسن للصفحات الفرعية */
    .nova-services-header {
      width: 100%;
      background: var(--bg-glass);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-bottom: var(--border-glass);
      padding: var(--spacing-xl) 0;
      text-align: center;
      box-shadow: var(--shadow-medium);
      margin-bottom: var(--spacing-2xl);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .nova-services-header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--spacing-lg);
    }

    .nova-services-logo {
      font-size: 2.2rem;
      font-weight: 800;
      color: var(--primary-color);
      text-shadow: 0 2px 10px rgba(99, 102, 241, 0.3);
      position: relative;
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .nova-services-logo::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
      transform: scaleX(0);
      transition: transform 0.3s ease;
      opacity: 0.7;
    }

    .nova-services-logo:hover::after {
      transform: scaleX(1);
    }

    .nova-services-logo:hover {
      color: var(--secondary-color);
    }

    .nova-services-nav {
      display: flex;
      gap: var(--spacing-md);
    }

    .nova-services-nav a {
      color: var(--text-secondary);
      text-decoration: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      background: var(--bg-card);
      border: var(--border-secondary);
      transition: all 0.3s ease;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
    }

    .nova-services-nav a:hover {
      background: var(--primary-color);
      color: var(--text-inverse);
      transform: translateY(-2px);
      box-shadow: var(--shadow-colored);
    }
    .main-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      max-width: 1200px;
      padding: var(--spacing-lg);
      flex: 1;
      gap: var(--spacing-xl);
    }

    .container {
      background: rgba(30, 30, 47, 0.95);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      max-width: 500px;
      width: 100%;
      border: 1px solid rgba(74, 144, 226, 0.3);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      position: relative;
      transform: translateZ(0);
      backface-visibility: hidden;
    }

    .container:hover {
      transform: translateY(-2px) translateZ(0);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      border-color: var(--primary-color);
    }
    h2 {
      margin-bottom: var(--spacing-xl);
      font-weight: 700;
      font-size: 1.8rem;
      color: var(--primary-color);
      text-align: center;
      position: relative;
      padding-bottom: var(--spacing-md);
      letter-spacing: 0.3px;
    }

    h2::before {
      content: '';
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 3px;
      background: var(--accent-color);
      border-radius: 2px;
      opacity: 0.8;
    }

    h2::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
      border-radius: 2px;
      opacity: 0.6;
    }
    input, select {
      width: 100%;
      padding: var(--spacing-md) var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      border-radius: var(--radius-md);
      border: 2px solid rgba(255, 255, 255, 0.1);
      font-size: 1rem;
      font-weight: 500;
      outline: none;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      color: var(--text-primary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
    }

    input::placeholder, select:invalid {
      color: var(--text-muted);
      font-weight: 400;
    }

    input:focus, select:focus {
      border-color: var(--primary-color);
      background: rgba(255, 255, 255, 0.08);
      box-shadow:
        0 0 0 2px rgba(74, 144, 226, 0.15),
        0 4px 15px rgba(74, 144, 226, 0.1);
      transform: translateY(-1px);
    }

    input:hover, select:hover {
      border-color: rgba(74, 144, 226, 0.3);
      background: rgba(255, 255, 255, 0.06);
    }

    /* تحسين تصميم القوائم المنسدلة */
    select {
      cursor: pointer;
      background-image: url("data:image/svg+xml;utf8,<svg fill='%234a90e2' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
      background-repeat: no-repeat;
      background-position: left var(--spacing-md) center;
      background-size: 18px;
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
    }
    button {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      padding: var(--spacing-lg) var(--spacing-xl);
      width: 100%;
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-bottom: var(--spacing-md);
      box-shadow:
        0 6px 20px rgba(74, 144, 226, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
      position: relative;
      overflow: hidden;
      letter-spacing: 0.3px;
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    button:hover::before {
      left: 100%;
    }

    button:hover {
      background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
      transform: translateY(-2px) scale(1.01);
      box-shadow:
        0 8px 25px rgba(74, 144, 226, 0.25),
        0 0 15px rgba(74, 144, 226, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    button:active {
      transform: translateY(-2px) scale(1.01);
      transition: all 0.1s ease;
    }

    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* أزرار ثانوية */
    .btn-secondary {
      background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    }

    .btn-secondary:hover {
      background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    }
    .status {
      margin-top: var(--spacing-lg);
      font-size: 1rem;
      font-weight: 500;
      min-height: 30px;
      white-space: pre-line;
      text-align: center;
      color: var(--text-secondary);
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }

    .status.success {
      color: var(--success-color);
      background: rgba(40, 167, 69, 0.08);
      border-color: var(--success-color);
      box-shadow: 0 0 10px rgba(40, 167, 69, 0.1);
    }

    .status.error {
      color: var(--error-color);
      background: rgba(220, 53, 69, 0.08);
      border-color: var(--error-color);
      box-shadow: 0 0 10px rgba(220, 53, 69, 0.1);
    }

    .whatsapp-link {
      display: none;
      margin-top: var(--spacing-lg);
      background: linear-gradient(135deg, var(--success-color), #128C7E);
      color: white;
      font-weight: 700;
      padding: var(--spacing-lg) var(--spacing-xl);
      border-radius: var(--radius-md);
      text-align: center;
      cursor: pointer;
      text-decoration: none;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 8px 25px rgba(37, 211, 102, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-sm);
    }

    .whatsapp-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .whatsapp-link:hover::before {
      left: 100%;
    }

    .whatsapp-link:hover {
      background: linear-gradient(135deg, #128C7E, var(--success-color));
      transform: translateY(-2px) scale(1.01);
      box-shadow:
        0 8px 25px rgba(40, 167, 69, 0.25),
        0 0 15px rgba(40, 167, 69, 0.15);
    }
    .loader {
      display: none;
      width: 50px;
      height: 50px;
      margin: var(--spacing-lg) auto;
      position: relative;
    }

    .loader::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 3px solid transparent;
      border-top: 3px solid var(--primary-color);
      border-right: 3px solid var(--secondary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loader::after {
      content: '';
      position: absolute;
      top: 6px;
      left: 6px;
      width: calc(100% - 12px);
      height: calc(100% - 12px);
      border: 2px solid transparent;
      border-bottom: 2px solid var(--accent-color);
      border-left: 2px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1.5s linear infinite reverse;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* تأثير النبض للودر */
    .loader {
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.1); }
    }
    .account-info {
      background: linear-gradient(135deg, rgba(26, 26, 46, 0.9), rgba(41, 42, 69, 0.9));
      backdrop-filter: blur(20px);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      margin-top: var(--spacing-xl);
      display: none;
      text-align: center;
      border: var(--border-glass);
      box-shadow:
        var(--shadow-medium),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
    }

    .account-info::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    }

    .account-info p {
      margin: var(--spacing-md) 0;
      line-height: 1.6;
      font-weight: 500;
      padding: var(--spacing-sm) var(--spacing-md);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-sm);
      border-left: 3px solid var(--primary-color);
      transition: all 0.3s ease;
    }

    .account-info p:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }

    .account-info strong {
      color: var(--primary-color);
      font-weight: 700;
    }
    /* تصميم القائمة المنسدلة العصري */
    .server-selection {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: var(--spacing-xl);
      position: relative;
    }

    .service-dropdown {
      width: 100%;
      padding: var(--spacing-lg) var(--spacing-xl);
      border-radius: var(--radius-lg);
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
      backdrop-filter: blur(15px);
      border: 2px solid rgba(74, 144, 226, 0.2);
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      text-align: right;
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: url("data:image/svg+xml;utf8,<svg fill='%234a90e2' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
      background-repeat: no-repeat;
      background-position: left var(--spacing-lg) center;
      background-size: 20px;
      box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
    }

    .service-dropdown::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .service-dropdown:hover::before {
      left: 100%;
    }

    .service-dropdown:hover {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
      transform: translateY(-2px);
      box-shadow:
        0 8px 25px rgba(74, 144, 226, 0.15),
        0 0 20px rgba(74, 144, 226, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }

    .service-dropdown:focus {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
      box-shadow:
        0 0 0 3px rgba(74, 144, 226, 0.2),
        0 8px 25px rgba(74, 144, 226, 0.2);
      outline: none;
      transform: translateY(-2px);
    }

    .service-dropdown option {
      background: var(--bg-secondary);
      color: var(--text-primary);
      padding: var(--spacing-md);
      font-weight: 500;
      border: none;
    }

    .service-dropdown option:hover {
      background: var(--bg-tertiary);
    }

    .service-dropdown option:checked {
      background: var(--primary-color);
      color: white;
    }

    /* تصميم البطاقات التفاعلية */
    .service-cards-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-xl);
      margin-bottom: var(--spacing-2xl);
      width: 100%;
    }

    .service-card {
      background: rgba(45, 55, 72, 0.9);
      border-radius: var(--radius-lg);
      border: 2px solid rgba(74, 144, 226, 0.3);
      padding: var(--spacing-xl);
      transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
      cursor: pointer;
      position: relative;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

      /* تحسينات الأداء القصوى */
      transform: translateZ(0);
      backface-visibility: hidden;
      perspective: 1000px;
    }

    .service-card:hover {
      transform: translateY(-4px) translateZ(0);
      border-color: var(--primary-color);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .service-card.active {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, rgba(74, 144, 226, 0.15), rgba(45, 55, 72, 0.9));
      box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 0 25px rgba(74, 144, 226, 0.3);
    }

    .service-card.featured {
      border-color: var(--accent-color);
      background: linear-gradient(135deg, rgba(80, 200, 120, 0.1), var(--bg-glass));
    }

    .service-card.featured:hover {
      border-color: var(--accent-color);
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(80, 200, 120, 0.2);
    }

    .featured-badge {
      position: absolute;
      top: -1px;
      right: var(--spacing-lg);
      background: linear-gradient(135deg, var(--accent-color), #45b049);
      color: white;
      padding: var(--spacing-xs) var(--spacing-md);
      border-radius: 0 0 var(--radius-md) var(--radius-md);
      font-size: 0.8rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
    }

    .service-card-header {
      text-align: center;
      margin-bottom: var(--spacing-lg);
    }

    .service-icon {
      width: 55px;
      height: 55px;
      background: var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--spacing-md);
      font-size: 1.4rem;
      color: white;
      box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
      transition: transform 0.2s ease;
      transform: translateZ(0);
    }

    .service-card:hover .service-icon {
      transform: scale(1.05) translateZ(0);
    }

    .service-card.featured .service-icon {
      background: linear-gradient(135deg, var(--accent-color), #45b049);
    }

    .service-card-header h3 {
      font-size: 1.4rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .service-badge {
      display: inline-block;
      background: rgba(74, 144, 226, 0.2);
      color: var(--primary-color);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .service-badge.premium {
      background: rgba(80, 200, 120, 0.2);
      color: var(--accent-color);
    }

    .service-card-body {
      margin-bottom: var(--spacing-lg);
    }

    .service-features {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .service-features li {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) 0;
      color: var(--text-secondary);
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .service-features li:hover {
      color: var(--text-primary);
      transform: translateX(-3px);
    }

    .service-features li i {
      color: var(--accent-color);
      font-size: 0.9rem;
      width: 16px;
    }

    .select-service-btn {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      padding: var(--spacing-md) var(--spacing-lg);
      border-radius: var(--radius-md);
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-sm);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
    }

    .select-service-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
    }

    .select-service-btn.premium {
      background: linear-gradient(135deg, var(--accent-color), #45b049);
      box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
    }

    .select-service-btn.premium:hover {
      box-shadow: 0 8px 25px rgba(80, 200, 120, 0.4);
    }

    /* تحسينات للأجهزة المختلفة */
    @media (max-width: 768px) {
      .service-cards-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .service-card {
        padding: var(--spacing-lg);
      }

      .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
      }

      .service-card-header h3 {
        font-size: 1.2rem;
      }

      .service-features li {
        font-size: 0.9rem;
      }

      .select-service-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
      }
    }

    @media (max-width: 480px) {
      .service-card {
        padding: var(--spacing-md);
      }

      .service-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
      }

      .featured-badge {
        font-size: 0.7rem;
        padding: 4px var(--spacing-xs);
      }
    }





    /* تحسينات إضافية للبطاقات */
    .service-card-footer {
      margin-top: auto;
    }

    .service-card {
      display: flex;
      flex-direction: column;
      min-height: 400px;
    }

    /* تأثير النبض للبطاقة المميزة */
    .service-card.featured {
      animation: featuredPulse 3s ease-in-out infinite;
    }

    @keyframes featuredPulse {
      0%, 100% {
        box-shadow:
          0 8px 32px rgba(0, 0, 0, 0.2),
          0 0 20px rgba(80, 200, 120, 0.2);
      }
      50% {
        box-shadow:
          0 8px 32px rgba(0, 0, 0, 0.2),
          0 0 30px rgba(80, 200, 120, 0.4);
      }
    }

    /* تحسين الأيقونات */
    .service-features li i.fas {
      background: linear-gradient(135deg, var(--accent-color), #45b049);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* تأثير التحميل للبطاقات */
    .service-card.loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .service-card.loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* تحسين النصوص */
    .service-card-header h3 {
      background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .service-card.featured .service-card-header h3 {
      background: linear-gradient(135deg, var(--text-primary), var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* تصميم بطاقات اختيار نوع الطلب */
    .action-cards-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
      width: 100%;
    }

    .action-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
      backdrop-filter: blur(15px);
      border-radius: var(--radius-lg);
      border: 2px solid rgba(74, 144, 226, 0.2);
      padding: var(--spacing-lg);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      position: relative;
      overflow: hidden;
      text-align: center;
      box-shadow:
        0 6px 25px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .action-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .action-card:hover::before {
      left: 100%;
    }

    .action-card:hover {
      transform: translateY(-5px) scale(1.02);
      border-color: var(--primary-color);
      box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 0 25px rgba(74, 144, 226, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }

    .action-card.active {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, rgba(74, 144, 226, 0.15), rgba(255, 255, 255, 0.08));
      box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(74, 144, 226, 0.3);
    }

    .action-card.premium {
      border-color: var(--accent-color);
      background: linear-gradient(135deg, rgba(80, 200, 120, 0.1), rgba(255, 255, 255, 0.06));
    }

    .action-card.premium:hover {
      border-color: var(--accent-color);
      box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 0 25px rgba(80, 200, 120, 0.2);
    }

    .action-card.premium.active {
      border-color: var(--accent-color);
      background: linear-gradient(135deg, rgba(80, 200, 120, 0.15), rgba(255, 255, 255, 0.08));
      box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(80, 200, 120, 0.3);
    }

    .premium-badge {
      position: absolute;
      top: -1px;
      right: var(--spacing-md);
      background: linear-gradient(135deg, var(--accent-color), #45b049);
      color: white;
      padding: 4px var(--spacing-sm);
      border-radius: 0 0 var(--radius-sm) var(--radius-sm);
      font-size: 0.7rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
    }

    .trial-badge {
      position: absolute;
      top: -1px;
      right: var(--spacing-md);
      background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
      color: white;
      padding: 4px var(--spacing-sm);
      border-radius: 0 0 var(--radius-sm) var(--radius-sm);
      font-size: 0.7rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(123, 104, 238, 0.3);
    }

    .action-card.trial-plus {
      border-color: var(--secondary-color);
      background: linear-gradient(135deg, rgba(123, 104, 238, 0.1), rgba(255, 255, 255, 0.06));
    }

    .action-card.trial-plus:hover {
      border-color: var(--secondary-color);
      box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 0 25px rgba(123, 104, 238, 0.2);
    }

    .action-card.trial-plus .action-icon {
      background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
      box-shadow: 0 6px 20px rgba(123, 104, 238, 0.3);
    }

    .action-btn.trial-plus {
      background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
      box-shadow: 0 4px 15px rgba(123, 104, 238, 0.3);
    }

    .action-btn.trial-plus:hover {
      box-shadow: 0 6px 20px rgba(123, 104, 238, 0.4);
    }

    .action-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--spacing-md);
      font-size: 1.3rem;
      color: white;
      box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
      transition: all 0.3s ease;
    }

    .action-card:hover .action-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
    }

    .action-card.premium .action-icon {
      background: linear-gradient(135deg, var(--accent-color), #45b049);
      box-shadow: 0 6px 20px rgba(80, 200, 120, 0.3);
    }

    .action-card.premium:hover .action-icon {
      box-shadow: 0 8px 25px rgba(80, 200, 120, 0.4);
    }

    .action-card h3 {
      font-size: 1.2rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .action-card p {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-bottom: var(--spacing-md);
      line-height: 1.5;
    }

    .action-features {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-lg);
    }

    .action-features span {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--text-secondary);
      font-size: 0.8rem;
      font-weight: 500;
      justify-content: center;
    }

    .action-features span i {
      color: var(--accent-color);
      width: 14px;
    }

    .action-btn {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      padding: var(--spacing-md) var(--spacing-lg);
      border-radius: var(--radius-md);
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
      text-transform: uppercase;
      letter-spacing: 0.3px;
      box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
    }

    .action-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
    }

    .action-btn.trial {
      background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
      box-shadow: 0 4px 15px rgba(123, 104, 238, 0.3);
    }

    .action-btn.trial:hover {
      box-shadow: 0 6px 20px rgba(123, 104, 238, 0.4);
    }

    .action-btn.premium {
      background: linear-gradient(135deg, var(--accent-color), #45b049);
      box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
    }

    .action-btn.premium:hover {
      box-shadow: 0 6px 20px rgba(80, 200, 120, 0.4);
    }

    /* تحسينات للأجهزة المختلفة */
    @media (max-width: 768px) {
      .action-cards-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .action-card {
        padding: var(--spacing-md);
      }

      .action-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
      }

      .action-card h3 {
        font-size: 1.1rem;
      }

      .action-features span {
        font-size: 0.75rem;
      }

      .action-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.85rem;
      }
    }

    @media (max-width: 480px) {
      .action-card {
        padding: var(--spacing-sm);
      }

      .action-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
      }

      .premium-badge {
        font-size: 0.6rem;
        padding: 3px 6px;
      }
    }



    /* تأثير النبض للبطاقة المميزة */
    .action-card.premium {
      animation: actionPulse 4s ease-in-out infinite;
    }

    @keyframes actionPulse {
      0%, 100% {
        box-shadow:
          0 6px 25px rgba(0, 0, 0, 0.15),
          0 0 15px rgba(80, 200, 120, 0.2);
      }
      50% {
        box-shadow:
          0 6px 25px rgba(0, 0, 0, 0.15),
          0 0 25px rgba(80, 200, 120, 0.4);
      }
    }

    /* تأثيرات إضافية */
    @keyframes bounce {
      0%, 20%, 60%, 100% {
        transform: translateY(0) scale(1);
      }
      40% {
        transform: translateY(-10px) scale(1.1);
      }
      80% {
        transform: translateY(-5px) scale(1.05);
      }
    }

    .action-card.loading {
      opacity: 0.7;
      pointer-events: none;
      position: relative;
    }

    .action-card.loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      border-radius: var(--radius-lg);
      z-index: 1;
    }

    /* تحسين التركيز للوصولية */
    .action-card:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
    }

    .action-card.premium:focus {
      outline-color: var(--accent-color);
    }

    /* تأثيرات اللمس للأجهزة المحمولة */
    @media (hover: none) and (pointer: coarse) {
      .action-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }

      .action-btn:active {
        transform: scale(0.95);
      }
    }

    /* تحسين الأنيميشن للأداء */
    .action-card,
    .action-icon,
    .action-btn {
      will-change: transform;
    }

    /* تأثير التدرج المتحرك للبطاقات المميزة */
    .action-card.premium::before {
      background: linear-gradient(90deg, transparent, rgba(80, 200, 120, 0.15), transparent);
    }

    /* تحسين الظلال للشاشات عالية الدقة */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      .action-card {
        box-shadow:
          0 3px 12px rgba(0, 0, 0, 0.15),
          inset 0 0.5px 0 rgba(255, 255, 255, 0.1);
      }

      .action-card:hover {
        box-shadow:
          0 8px 18px rgba(0, 0, 0, 0.25),
          0 0 12px rgba(74, 144, 226, 0.2),
          inset 0 0.5px 0 rgba(255, 255, 255, 0.15);
      }
    }
    .server-option {
      display: none;
    }
    .form-container {
      display: none !important;
      animation: fadeIn 0.5s ease;
    }
    .form-container.active {
      display: block !important;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    footer {
      width: 100%;
      background: linear-gradient(135deg, var(--bg-card) 0%, rgba(15, 15, 26, 0.95) 100%);
      backdrop-filter: blur(20px);
      border-top: var(--border-glass);
      padding: var(--spacing-2xl) 0 var(--spacing-xl);
      text-align: center;
      margin-top: var(--spacing-2xl);
      box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
      position: relative;
    }

    footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, var(--primary-color), var(--secondary-color), transparent);
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 var(--spacing-lg);
    }

    .social-links {
      margin: var(--spacing-xl) 0;
      display: flex;
      justify-content: center;
      gap: var(--spacing-lg);
    }

    .social-links a {
      color: var(--text-secondary);
      font-size: 1.8rem;
      padding: var(--spacing-md);
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
    }

    .social-links a:hover {
      color: var(--primary-color);
      background: rgba(74, 144, 226, 0.08);
      border-color: var(--primary-color);
      transform: translateY(-3px) scale(1.05);
      box-shadow:
        0 6px 20px rgba(74, 144, 226, 0.15),
        0 0 10px rgba(74, 144, 226, 0.1);
    }

    .copyright {
      font-size: 0.95rem;
      color: var(--text-muted);
      margin-top: var(--spacing-lg);
      padding-top: var(--spacing-lg);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      font-weight: 500;
    }

    /* تحسينات الاستجابة المحسنة */
    @media (max-width: 1024px) {
      .main-content {
        padding: var(--spacing-md);
      }

      .container {
        max-width: 600px;
      }
    }

    @media (max-width: 768px) {
      :root {
        --spacing-xs: 6px;
        --spacing-sm: 10px;
        --spacing-md: 14px;
        --spacing-lg: 20px;
        --spacing-xl: 28px;
        --spacing-2xl: 40px;
      }

      .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
      }

      .logo {
        font-size: 1.8rem;
      }

      .container {
        max-width: 100%;
        padding: var(--spacing-xl) var(--spacing-lg);
        margin: 0 var(--spacing-sm);
      }

      h2 {
        font-size: 1.6rem;
      }

      input, select, button {
        font-size: 1rem;
      }

      .social-links {
        flex-wrap: wrap;
        gap: var(--spacing-md);
      }

      .social-links a {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
      }
    }

    @media (max-width: 480px) {
      .logo {
        font-size: 1.6rem;
      }

      h2 {
        font-size: 1.4rem;
      }

      .container {
        padding: var(--spacing-lg) var(--spacing-md);
        border-radius: var(--radius-md);
      }

      input, select {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.95rem;
      }

      button {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
      }

      .account-info {
        padding: var(--spacing-lg);
      }

      .social-links a {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
      }
    }

    @media (max-width: 360px) {
      .main-content {
        padding: var(--spacing-sm);
      }

      .container {
        margin: 0;
        border-radius: var(--radius-sm);
      }

      .logo {
        font-size: 1.4rem;
      }

      h2 {
        font-size: 1.2rem;
      }
    }

    /* تحسينات للشاشات الكبيرة */
    @media (min-width: 1200px) {
      .container {
        max-width: 550px;
      }

      .main-content {
        max-width: 1400px;
      }
    }
    .payment-info {
      background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(41, 42, 69, 0.8));
      backdrop-filter: blur(15px);
      padding: var(--spacing-lg);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-lg);
      border: var(--border-glass);
      box-shadow: var(--shadow-light);
      position: relative;
      overflow: hidden;
    }

    .payment-info::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
    }

    .payment-info h3 {
      color: var(--primary-color);
      margin-bottom: var(--spacing-md);
      font-weight: 700;
      text-align: center;
      font-size: 1.1rem;
    }

    .payment-info p {
      margin: var(--spacing-sm) 0;
      padding: var(--spacing-xs) var(--spacing-sm);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-sm);
      border-right: 2px solid var(--primary-color);
      transition: all 0.3s ease;
    }

    .payment-info p:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(-3px);
    }



    /* تأثير التركيز المحسن */
    input:focus, select:focus {
      animation: focusPulse 0.4s ease;
    }

    @keyframes focusPulse {
      0% { box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.2); }
      70% { box-shadow: 0 0 0 6px rgba(74, 144, 226, 0); }
      100% { box-shadow: 0 0 0 0 rgba(74, 144, 226, 0); }
    }

    /* تأثير الكتابة */
    .typing-effect {
      overflow: hidden;
      border-right: 2px solid var(--primary-color);
      white-space: nowrap;
      animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
    }

    @keyframes typing {
      from { width: 0; }
      to { width: 100%; }
    }

    @keyframes blink-caret {
      from, to { border-color: transparent; }
      50% { border-color: var(--primary-color); }
    }

    /* تحسينات الأداء والأجهزة الضعيفة */
    @media (prefers-reduced-motion: reduce) {
      html {
        scroll-behavior: auto;
      }

      /* تعطيل جميع الأنيميشن للمستخدمين الذين يفضلون تقليل الحركة */
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }

    /* تحسينات الأداء القصوى */
    .service-card,
    .action-card,
    .container {
      /* تحسين الرسم */
      contain: layout style;
      /* تحسين الذاكرة */
      content-visibility: auto;
    }

    /* تحسين التمرير */
    * {
      scroll-behavior: smooth;
    }

    /* تحسين الأداء للأجهزة الضعيفة جداً */
    @media (max-width: 320px), (max-height: 568px) {
      .service-card,
      .action-card {
        transition: none !important;
        transform: none !important;
      }

      .service-card:hover,
      .action-card:hover {
        transform: none !important;
      }
    }

    /* تحسين للشبكة البطيئة */
    @media (max-width: 480px) and (max-height: 800px) {
      .container {
        background: rgba(30, 30, 47, 0.98);
      }

      .service-card,
      .action-card {
        background: rgba(45, 55, 72, 0.95);
      }
    }

    /* تحسينات إضافية للأداء */
    .service-card,
    .action-card {
      /* تحسين الرسم */
      will-change: transform;
      /* تحسين الذاكرة */
      contain: layout style paint;
    }

    /* تحسين للأجهزة الضعيفة جداً */
    @media (max-width: 320px) and (max-height: 480px) {
      body {
        font-size: 14px;
      }

      .container {
        padding: var(--spacing-md);
        margin: var(--spacing-xs);
      }

      .service-card,
      .action-card {
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
      }

      .service-icon,
      .action-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }
    }

    /* تحسين للبطارية الضعيفة */
    @media (max-width: 768px) and (prefers-reduced-motion: reduce) {
      * {
        transition: none !important;
        animation: none !important;
      }
    }

    /* تحسين عنوان الصفحة */
    .page-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
    }

    .page-header h2 {
      color: var(--primary-color);
      margin-bottom: var(--spacing-sm);
      font-size: 1.8rem;
    }

    .page-header p {
      color: var(--text-secondary);
      font-size: 1rem;
      margin: 0;
    }

    /* التأكد من إخفاء النماذج افتراضياً */
    #nova-form,
    #nova-plus-form {
      display: none !important;
      visibility: hidden;
      opacity: 0;
    }

    #nova-form.active,
    #nova-plus-form.active {
      display: block !important;
      visibility: visible;
      opacity: 1;
    }

    /* تصميم زر العودة لاختيار نوع الطلب */
    .back-to-action-btn {
      background: rgba(74, 144, 226, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      border-radius: var(--radius-md);
      padding: var(--spacing-sm) var(--spacing-md);
      color: var(--primary-color);
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-lg);
      width: fit-content;
    }

    .back-to-action-btn:hover {
      background: rgba(74, 144, 226, 0.2);
      transform: translateX(2px);
      box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
    }

    .back-to-action-btn:active {
      transform: translateX(1px);
    }

    .back-to-action-btn i {
      font-size: 0.8rem;
      transition: transform 0.2s ease;
    }

    .back-to-action-btn:hover i {
      transform: translateX(2px);
    }

    /* متغيرات ديناميكية لشاشة التقدم */
    :root {
      --progress-width: min(90vw, 520px);
      --progress-padding: clamp(1rem, 4vw, 2rem);
      --progress-font-title: clamp(1.3rem, 4vw, 1.8rem);
      --progress-font-subtitle: clamp(0.9rem, 3vw, 1.1rem);
      --progress-font-text: clamp(0.8rem, 2.5vw, 1rem);
      --progress-spacing: clamp(0.8rem, 3vw, 1.5rem);
      --progress-icon-size: clamp(2.5rem, 8vw, 3.5rem);
    }

    /* تصميم شاشة التقدم المحسن */
    .progress-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.95),
        rgba(30, 30, 47, 0.9),
        rgba(45, 55, 72, 0.85)
      );
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: progressFadeIn 0.4s ease;
      padding: var(--progress-spacing);
      box-sizing: border-box;
    }

    @keyframes progressFadeIn {
      from {
        opacity: 0;
        backdrop-filter: blur(0px);
      }
      to {
        opacity: 1;
        backdrop-filter: blur(15px);
      }
    }

    .progress-container {
      background: linear-gradient(135deg,
        rgba(30, 30, 47, 0.98),
        rgba(45, 55, 72, 0.95),
        rgba(74, 144, 226, 0.08)
      );
      border-radius: clamp(12px, 3vw, 20px);
      padding: var(--progress-padding);
      width: var(--progress-width);
      max-width: 100%;
      text-align: center;
      border: 2px solid rgba(74, 144, 226, 0.4);
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.6),
        0 0 30px rgba(74, 144, 226, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
      animation: progressSlideIn 0.5s ease;
    }

    @keyframes progressSlideIn {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* تأثير الخلفية المتحركة */
    .progress-container::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
        transparent,
        rgba(74, 144, 226, 0.05),
        transparent
      );
      animation: progressBackgroundMove 8s linear infinite;
      pointer-events: none;
    }

    @keyframes progressBackgroundMove {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .progress-header {
      margin-bottom: var(--progress-spacing);
      position: relative;
      z-index: 2;
    }

    .progress-icon {
      font-size: var(--progress-icon-size);
      color: var(--primary-color);
      margin-bottom: var(--progress-spacing);
      display: block;
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }

    .progress-icon i {
      animation: progressIconSpin 2s linear infinite;
    }

    .progress-icon.success {
      color: var(--accent-color);
      text-shadow: 0 0 20px rgba(80, 200, 120, 0.6);
    }

    .progress-icon.success i {
      animation: progressIconSuccess 0.6s ease;
    }

    .progress-icon.error {
      color: #ff4757;
      text-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
    }

    .progress-icon.error i {
      animation: progressIconError 0.6s ease;
    }

    @keyframes progressIconSpin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes progressIconSuccess {
      0% { transform: scale(1); }
      50% { transform: scale(1.2); }
      100% { transform: scale(1); }
    }

    @keyframes progressIconError {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }

    #progress-title {
      color: var(--text-primary);
      margin-bottom: calc(var(--progress-spacing) * 0.5);
      font-size: var(--progress-font-title);
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      line-height: 1.3;
    }

    #progress-subtitle {
      color: var(--text-secondary);
      font-size: var(--progress-font-subtitle);
      margin: 0;
      opacity: 0.9;
      line-height: 1.4;
    }

    /* شريط التقدم المحسن */
    .progress-bar-container {
      margin: var(--progress-spacing) 0;
      position: relative;
      z-index: 2;
    }

    .progress-bar {
      width: 100%;
      height: clamp(14px, 3vw, 18px);
      background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.08),
        rgba(255, 255, 255, 0.12),
        rgba(255, 255, 255, 0.08)
      );
      border-radius: clamp(7px, 1.5vw, 9px);
      overflow: hidden;
      margin-bottom: var(--progress-spacing);
      position: relative;
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 1px 2px rgba(255, 255, 255, 0.1);
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg,
        #4A90E2,
        #50C878,
        #FFD700,
        #50C878,
        #4A90E2
      );
      background-size: 200% 100%;
      border-radius: clamp(6px, 1.5vw, 8px);
      width: 0%;
      transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      animation: progressGradientMove 3s ease-in-out infinite;
    }

    @keyframes progressGradientMove {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .progress-fill::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      );
      animation: progressShimmer 2.5s infinite;
    }

    @keyframes progressShimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .progress-percentage {
      color: var(--primary-color);
      font-weight: 700;
      font-size: var(--progress-font-title);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      margin-top: calc(var(--progress-spacing) * 0.5);
    }

    /* الوقت المتبقي المحسن */
    .progress-time {
      margin: var(--progress-spacing) 0;
      position: relative;
      z-index: 2;
    }

    .time-remaining {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: calc(var(--progress-spacing) * 0.5);
      color: var(--text-secondary);
      font-size: var(--progress-font-text);
      background: rgba(255, 255, 255, 0.05);
      padding: calc(var(--progress-spacing) * 0.7);
      border-radius: clamp(8px, 2vw, 12px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(5px);
    }

    .time-remaining i {
      color: var(--primary-color);
      font-size: 1.2em;
      animation: progressTimePulse 2s ease-in-out infinite;
    }

    @keyframes progressTimePulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.1); }
    }

    #time-remaining {
      color: var(--primary-color);
      font-weight: 700;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 1.1em;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      min-width: 3.5em;
      text-align: center;
    }

    /* معلومات إضافية */
    .progress-info {
      display: flex;
      justify-content: center;
      gap: var(--progress-spacing);
      margin-top: calc(var(--progress-spacing) * 0.8);
      opacity: 0.8;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: calc(var(--progress-spacing) * 0.3);
      font-size: calc(var(--progress-font-text) * 0.9);
      color: var(--text-secondary);
      background: rgba(255, 255, 255, 0.03);
      padding: calc(var(--progress-spacing) * 0.4) calc(var(--progress-spacing) * 0.6);
      border-radius: clamp(6px, 1.5vw, 8px);
      border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .info-item i {
      color: var(--primary-color);
      font-size: 0.9em;
      opacity: 0.8;
    }

    @media (max-width: 480px) {
      .progress-info {
        flex-direction: column;
        gap: calc(var(--progress-spacing) * 0.5);
        align-items: center;
      }

      .info-item {
        justify-content: center;
        min-width: 120px;
      }
    }

    /* تأثيرات النجاح */
    .info-item.success {
      background: rgba(80, 200, 120, 0.1);
      border-color: rgba(80, 200, 120, 0.3);
      color: var(--accent-color);
    }

    .info-item.success i {
      color: var(--accent-color);
    }

    @keyframes celebrationPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    /* خطوات العملية المحسنة */
    .progress-steps {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: calc(var(--progress-spacing) * 0.8);
      margin-top: var(--progress-spacing);
      position: relative;
      z-index: 2;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: calc(var(--progress-spacing) * 0.5);
      padding: var(--progress-spacing);
      border-radius: clamp(10px, 2.5vw, 15px);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 0.4;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.08);
      position: relative;
      overflow: hidden;
    }

    .step::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .step.active {
      opacity: 1;
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.15),
        rgba(74, 144, 226, 0.08)
      );
      border: 1px solid rgba(74, 144, 226, 0.4);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(74, 144, 226, 0.2);
    }

    .step.active::before {
      left: 100%;
    }

    .step.completed {
      opacity: 1;
      background: linear-gradient(135deg,
        rgba(80, 200, 120, 0.15),
        rgba(80, 200, 120, 0.08)
      );
      border: 1px solid rgba(80, 200, 120, 0.4);
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(80, 200, 120, 0.15);
    }

    .step i {
      font-size: clamp(1.3rem, 4vw, 1.8rem);
      color: var(--text-secondary);
      transition: all 0.3s ease;
    }

    .step.active i {
      color: var(--primary-color);
      animation: progressStepPulse 2s ease-in-out infinite;
      text-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
    }

    .step.completed i {
      color: var(--accent-color);
      animation: progressStepSuccess 0.6s ease;
      text-shadow: 0 0 10px rgba(80, 200, 120, 0.5);
    }

    @keyframes progressStepPulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.15); opacity: 0.8; }
    }

    @keyframes progressStepSuccess {
      0% { transform: scale(1); }
      50% { transform: scale(1.3); }
      100% { transform: scale(1); }
    }

    .step span {
      font-size: var(--progress-font-text);
      color: var(--text-secondary);
      text-align: center;
      line-height: 1.3;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .step.active span {
      color: var(--primary-color);
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .step.completed span {
      color: var(--accent-color);
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* تحسينات شاملة للأجهزة المحمولة */
    @media (max-width: 768px) {
      .progress-overlay {
        padding: 1rem;
        backdrop-filter: blur(10px);
      }

      .progress-container {
        width: 95vw;
        max-width: none;
        padding: 1.5rem;
        margin: 0;
        border-radius: 15px;
      }

      .progress-steps {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 1.5rem;
      }

      .step {
        flex-direction: row;
        justify-content: flex-start;
        text-align: right;
        padding: 1rem;
        gap: 1rem;
      }

      .step i {
        font-size: 1.5rem;
        min-width: 2rem;
      }

      .step span {
        font-size: 0.9rem;
        text-align: right;
      }

      .progress-bar {
        height: 16px;
      }

      .time-remaining {
        flex-direction: column;
        gap: 0.5rem;
        padding: 1rem;
      }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @media (max-width: 480px) {
      :root {
        --progress-font-title: 1.2rem;
        --progress-font-subtitle: 0.9rem;
        --progress-font-text: 0.8rem;
        --progress-icon-size: 2.5rem;
      }

      .progress-container {
        padding: 1rem;
        border-radius: 12px;
      }

      .progress-container::before {
        display: none; /* إزالة التأثير المتحرك للأداء */
      }

      .step {
        padding: 0.8rem;
      }
    }

    /* تحسينات للأجهزة ذات الأداء المحدود */
    @media (prefers-reduced-motion: reduce) {
      .progress-overlay,
      .progress-container,
      .progress-icon i,
      .progress-fill,
      .step {
        animation: none !important;
        transition: none !important;
      }

      .progress-container::before {
        display: none;
      }

      .progress-fill::after {
        display: none;
      }
    }

    /* تحسينات للشاشات عالية الدقة */
    @media (min-width: 1200px) {
      .progress-container {
        max-width: 600px;
        padding: 3rem;
      }

      .progress-steps {
        gap: 2rem;
        margin-top: 2.5rem;
      }
    }

    /* تأثير إبراز البيانات بعد إكمال التقدم */
    @keyframes highlightData {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.4);
      }
      50% {
        transform: scale(1.02);
        box-shadow: 0 0 20px 10px rgba(74, 144, 226, 0.2);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
      }
    }

    /* تصميم رسالة النجاح في البيانات */
    .success-header {
      background: linear-gradient(135deg, rgba(80, 200, 120, 0.1), rgba(74, 144, 226, 0.1));
      border: 1px solid rgba(80, 200, 120, 0.3);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      text-align: center;
    }

    .success-header i {
      font-size: 2.5rem;
      color: var(--accent-color);
      margin-bottom: var(--spacing-sm);
      display: block;
    }

    .success-header h3 {
      color: var(--accent-color);
      margin: 0 0 var(--spacing-sm) 0;
      font-size: 1.3rem;
    }

    .success-header p {
      color: var(--text-secondary);
      margin: 0;
      font-size: 1rem;
    }

    /* نموذج التحقق المنبثق */
    .verification-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
      z-index: 10000;
      animation: fadeIn 0.3s ease;
    }

    .verification-modal.show {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      background: var(--bg-card);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-heavy);
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      animation: slideUp 0.3s ease;
    }

    .modal-header {
      padding: var(--spacing-xl);
      border-bottom: 1px solid rgba(74, 144, 226, 0.2);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h3 {
      margin: 0;
      color: var(--primary-color);
      font-size: var(--font-size-xl);
    }

    .modal-close {
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 1.5rem;
      cursor: pointer;
      padding: var(--spacing-sm);
      border-radius: var(--radius-md);
      transition: all 0.3s ease;
    }

    .modal-close:hover {
      background: rgba(255, 0, 0, 0.1);
      color: #ff4757;
    }

    .modal-body {
      padding: var(--spacing-xl);
    }

    .step {
      text-align: center;
    }

    .step.hidden {
      display: none;
    }

    .step-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto var(--spacing-lg);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: white;
    }

    .step-icon.success {
      background: linear-gradient(135deg, #2ed573, #1e90ff);
    }

    .step-description {
      color: var(--text-secondary);
      margin-bottom: var(--spacing-lg);
      line-height: 1.6;
    }

    .form-group {
      margin-bottom: var(--spacing-lg);
      text-align: left;
    }

    .form-group label {
      display: block;
      margin-bottom: var(--spacing-sm);
      color: var(--text-primary);
      font-weight: 600;
    }

    .form-group input {
      width: 100%;
      padding: var(--spacing-md);
      border: 2px solid rgba(74, 144, 226, 0.2);
      border-radius: var(--radius-md);
      background: var(--bg-secondary);
      color: var(--text-primary);
      font-size: var(--font-size-md);
      transition: all 0.3s ease;
      box-sizing: border-box;
    }

    .form-group input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    }

    .form-hint {
      display: block;
      margin-top: var(--spacing-xs);
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
    }

    .modal-btn {
      padding: var(--spacing-md) var(--spacing-xl);
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--font-size-md);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-sm);
      min-width: 150px;
      justify-content: center;
    }

    .modal-btn.primary {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
    }

    .modal-btn.primary:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-light);
    }

    .modal-btn.secondary {
      background: var(--bg-secondary);
      color: var(--text-secondary);
      border: 1px solid rgba(74, 144, 226, 0.2);
    }

    .modal-btn.secondary:hover {
      background: rgba(74, 144, 226, 0.1);
      color: var(--primary-color);
    }

    .modal-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none !important;
    }

    .timer-container {
      background: rgba(74, 144, 226, 0.1);
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      margin: var(--spacing-lg) 0;
      color: var(--primary-color);
      font-weight: 600;
    }

    .modal-actions {
      display: flex;
      gap: var(--spacing-md);
      justify-content: center;
      flex-wrap: wrap;
    }

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-md);
      margin: var(--spacing-xl) 0;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(74, 144, 226, 0.2);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .modal-footer {
      padding: var(--spacing-lg) var(--spacing-xl);
      border-top: 1px solid rgba(74, 144, 226, 0.2);
    }

    .status-message {
      text-align: center;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      font-weight: 600;
    }

    .status-message.success {
      background: rgba(46, 213, 115, 0.1);
      color: #2ed573;
      border: 1px solid rgba(46, 213, 115, 0.3);
    }

    .status-message.error {
      background: rgba(255, 71, 87, 0.1);
      color: #ff4757;
      border: 1px solid rgba(255, 71, 87, 0.3);
    }

    .status-message.info {
      background: rgba(74, 144, 226, 0.1);
      color: var(--primary-color);
      border: 1px solid rgba(74, 144, 226, 0.3);
    }

    /* الرسوم المتحركة */
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* التصميم المتجاوب للنموذج */
    @media (max-width: 768px) {
      .modal-content {
        width: 95%;
        margin: var(--spacing-md);
      }

      .modal-header,
      .modal-body,
      .modal-footer {
        padding: var(--spacing-lg);
      }

      .step-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
      }

      .modal-actions {
        flex-direction: column;
      }

      .modal-btn {
        width: 100%;
      }

      .otp-input-group {
        flex-direction: column;
        gap: 15px;
      }

      #sendOtpBtn {
        width: 100%;
        padding: 15px;
      }

      #otpCode {
        font-size: 16px;
        letter-spacing: 2px;
      }
    }

    /* تنسيقات نظام التحقق من OTP */
    .otp-verification-section {
      background: rgba(74, 144, 226, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      border-radius: 12px;
      padding: 20px;
      margin: 20px 0;
    }

    .otp-input-group {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    #otpCode {
      flex: 1;
      padding: 15px;
      border: 2px solid rgba(106, 252, 255, 0.3);
      border-radius: 10px;
      background: rgba(30, 30, 47, 0.7);
      color: white;
      font-size: 18px;
      text-align: center;
      letter-spacing: 3px;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    #otpCode:focus {
      outline: none;
      border-color: #4a90e2;
      box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
      background: rgba(30, 30, 47, 0.9);
    }

    #otpCode:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    #sendOtpBtn {
      padding: 15px 25px;
      background: linear-gradient(135deg, #4a90e2, #357abd);
      color: white;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      font-size: 14px;
    }

    #sendOtpBtn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(74, 144, 226, 0.4);
    }

    #sendOtpBtn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    #otpStatus {
      font-size: 14px;
      text-align: center;
      margin-top: 12px;
      padding: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    #otpStatus.success {
      background: rgba(46, 213, 115, 0.2);
      color: #2ed573;
      border: 1px solid #2ed573;
    }

    #otpStatus.error {
      background: rgba(255, 71, 87, 0.2);
      color: #ff4757;
      border: 1px solid #ff4757;
    }

    #otpStatus.info {
      background: rgba(74, 144, 226, 0.2);
      color: #4a90e2;
      border: 1px solid #4a90e2;
    }

    #resendTimer {
      font-size: 12px;
      text-align: center;
      color: #999;
      margin-top: 8px;
    }

    /* حقل التحقق المدمج */
    .verification-input-container {
      position: relative;
      display: flex;
      align-items: center;
      margin: 15px 0;
    }

    /* تأثير بصري للحاوية عند التركيز */
    .verification-input-container:focus-within .verify-btn {
      box-shadow: 0 3px 10px rgba(255, 107, 107, 0.5);
      background: linear-gradient(135deg, #ee5a52, #ff6b6b);
    }

    /* تحسين التناسق البصري */
    .verification-input-container:focus-within input {
      border-color: #6afcff;
    }

    .verification-input-container input {
      width: 100%;
      background: rgba(30, 30, 47, 0.8);
      border: 2px solid rgba(106, 252, 255, 0.3);
      border-radius: 12px;
      color: white;
      padding: 15px 20px 15px 62px; /* مساحة مناسبة للزر الأعرض */
      font-size: 16px;
      text-align: center;
      letter-spacing: 2px;
      font-weight: 500;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .verification-input-container input:focus {
      outline: none;
      border-color: #6afcff;
      box-shadow: 0 0 0 3px rgba(106, 252, 255, 0.2), 0 4px 15px rgba(0, 0, 0, 0.3);
      background: rgba(30, 30, 47, 0.95);
      transform: translateY(-1px);
    }

    .verification-input-container input:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: rgba(30, 30, 47, 0.5);
    }

    .verification-input-container input::placeholder {
      color: rgba(255, 255, 255, 0.5);
      text-align: center;
    }

    /* تحسين التباعد عند وجود الزر */
    .verification-input-container input:focus {
      padding-left: 66px; /* مساحة مناسبة عند التركيز */
    }

    /* زر التحقق المدمج */
    .verify-btn {
      position: absolute;
      left: 4px;
      top: 4px;
      bottom: 4px;
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      border: none;
      padding: 0 12px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
      z-index: 2;
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .verify-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #ee5a52, #ff6b6b);
      transform: translateX(-1px);
      box-shadow: 0 3px 8px rgba(255, 107, 107, 0.4);
    }

    .verify-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: linear-gradient(135deg, #666, #555);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .verify-btn:active {
      transform: translateX(0px);
      box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
    }

    /* رسائل الحالة */
    .otp-status-message {
      font-size: 14px;
      text-align: center;
      margin-top: 10px;
      padding: 8px 15px;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
      display: none;
    }

    .otp-status-message.success {
      background: rgba(46, 213, 115, 0.15);
      color: #2ed573;
      border: 1px solid rgba(46, 213, 115, 0.3);
    }

    .otp-status-message.error {
      background: rgba(255, 71, 87, 0.15);
      color: #ff4757;
      border: 1px solid rgba(255, 71, 87, 0.3);
    }

    .otp-status-message.info {
      background: rgba(74, 144, 226, 0.15);
      color: #4a90e2;
      border: 1px solid rgba(74, 144, 226, 0.3);
    }

    /* عداد إعادة الإرسال */
    .resend-timer {
      font-size: 12px;
      text-align: center;
      color: #999;
      margin-top: 5px;
      font-weight: 500;
    }

    /* تصميم متجاوب للجوال */
    @media (max-width: 768px) {
      .verification-input-container input {
        padding: 12px 15px 12px 48px;
        font-size: 15px;
        letter-spacing: 1.5px;
      }

      .verify-btn {
        width: 42px;
        left: 3px;
        top: 3px;
        bottom: 3px;
        font-size: 11px;
        padding: 0 10px;
      }

      .otp-status-message {
        font-size: 13px;
        padding: 6px 12px;
      }
    }

    @media (max-width: 480px) {
      .verification-input-container input {
        padding: 10px 12px 10px 42px;
        font-size: 14px;
        letter-spacing: 1px;
      }

      .verify-btn {
        width: 36px;
        left: 3px;
        top: 3px;
        bottom: 3px;
        font-size: 10px;
        padding: 0 8px;
      }
    }

    /* تحديث تنسيق زر الطلب */
    #requestBtn:disabled, #altRequestBtn:disabled {
      opacity: 0.5 !important;
      cursor: not-allowed !important;
      transform: none !important;
    }

    #requestBtn.verified, #altRequestBtn.verified {
      opacity: 1 !important;
      cursor: pointer !important;
      background: linear-gradient(135deg, #2ed573, #20bf6b) !important;
    }

    #requestBtn.verified:hover, #altRequestBtn.verified:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 10px 25px rgba(46, 213, 115, 0.4) !important;
    }

  </style>
</head>
<body>
  <!-- مؤشر تحميل بسيط -->
  <div class="page-loader" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: var(--bg-primary); display: flex; align-items: center; justify-content: center; z-index: 9999;">
    <div style="color: var(--primary-color); font-size: 1.2rem;">
      <i class="fas fa-spinner fa-spin" style="margin-left: 10px;"></i>
      جاري التحميل...
    </div>
  </div>
  <!-- Header المحسن -->
  <header class="nova-services-header">
    <div class="nova-services-header-content">
      <a href="index.html" class="nova-services-logo">Nova Yemen</a>

      <div class="nova-header-controls">
        <!-- زر تبديل الوضع الليلي/النهاري -->
        <button class="nova-theme-toggle" title="تبديل الوضع الليلي/النهاري">
          <i class="fas fa-sun nova-theme-toggle-icon sun"></i>
          <i class="fas fa-moon nova-theme-toggle-icon moon"></i>
        </button>

        <nav class="nova-services-nav">
          <a href="index.html">
            <i class="fas fa-home"></i> الصفحة الرئيسية
          </a>
        </nav>

        <!-- زر قائمة الهامبرغر للجوال -->
        <button class="nova-mobile-menu-btn">
          <div class="nova-hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>
  </header>

  <!-- القائمة المحمولة -->
  <div class="nova-mobile-overlay"></div>
  <div class="nova-mobile-menu">
    <div class="nova-mobile-menu-header">
      <div class="nova-logo">Nova Yemen</div>
      <button class="nova-mobile-menu-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <nav class="nova-mobile-menu-nav">
      <a href="index.html" class="nova-nav-link">
        <i class="fas fa-home"></i> الصفحة الرئيسية
      </a>
      <a href="#" class="nova-nav-link active">
        <i class="fas fa-cog"></i> الخدمات
      </a>
    </nav>
  </div>

  <div class="main-content">
    <div class="container">
      <div class="page-header">
        <h2>اختر نوع الخدمة</h2>
        <p>اختر الخدمة المناسبة لك للبدء</p>

        <!-- زر التواصل السريع -->
        <div class="nova-text-center nova-mt-lg">
          <button onclick="window.openContactModal()" class="nova-btn nova-btn-success nova-btn-lg">
            <i class="fas fa-phone"></i> تحتاج مساعدة؟ تواصل معنا
          </button>
        </div>
      </div>

      <!-- تصميم البطاقات التفاعلية -->
      <div class="service-cards-container">
        <div class="service-card" data-service="nova" id="nova-card">
          <div class="service-card-header">
            <div class="service-icon">
              <i class="fas fa-tv"></i>
            </div>
            <h3>نوفا</h3>
            <span class="service-badge">الأساسية</span>
          </div>
          <div class="service-card-body">
            <ul class="service-features">
              <li><i class="fas fa-check"></i> قنوات عربية وعالمية</li>
              <li><i class="fas fa-check"></i> جودة HD</li>
              <li><i class="fas fa-check"></i> تجربة مجانية 12 ساعة</li>
              <li><i class="fas fa-check"></i> دعم فني 24/7</li>
            </ul>
          </div>
          <div class="service-card-footer">
            <button class="select-service-btn" data-service="nova">
              <i class="fas fa-arrow-left"></i>
              اختيار نوفا
            </button>
          </div>
        </div>

        <div class="service-card featured" data-service="nova-plus" id="nova-plus-card">
          <div class="featured-badge">الأكثر شعبية</div>
          <div class="service-card-header">
            <div class="service-icon">
              <i class="fas fa-crown"></i>
            </div>
            <h3>نوفا Plus</h3>
            <span class="service-badge premium">المتقدمة</span>
          </div>
          <div class="service-card-body">
            <ul class="service-features">
              <li><i class="fas fa-check"></i> جميع مميزات نوفا</li>
              <li><i class="fas fa-check"></i> قنوات إضافية حصرية</li>
              <li><i class="fas fa-check"></i> جودة 4K</li>
              <li><i class="fas fa-check"></i> تجربة مجانية 24 ساعة</li>
              <li><i class="fas fa-check"></i> أولوية في الدعم</li>
            </ul>
          </div>
          <div class="service-card-footer">
            <button class="select-service-btn premium" data-service="nova-plus">
              <i class="fas fa-arrow-left"></i>
              اختيار نوفا Plus
            </button>
          </div>
        </div>
      </div>



      <!-- نموذج نوفا (السيرفر الأول) -->
      <div class="form-container" id="nova-form">
        <h2>خدمة نوفا</h2>

        <!-- بطاقات اختيار نوع الطلب لنوفا -->
        <div class="action-cards-container">
          <div class="action-card" data-action="trial" id="nova-trial-card">
            <div class="action-icon">
              <i class="fas fa-play-circle"></i>
            </div>
            <h3>تجربة مجانية</h3>
            <p>احصل على حساب تجريبي لمدة 12 ساعة</p>
            <div class="action-features">
              <span><i class="fas fa-clock"></i> 12 ساعة مجاناً</span>
              <span><i class="fas fa-tv"></i> جميع القنوات</span>
              <span><i class="fas fa-mobile-alt"></i> جميع الأجهزة</span>
            </div>
            <button class="action-btn trial" onclick="window.location.href='login.html'">
              <i class="fas fa-rocket"></i>
              ابدأ التجربة المجانية
            </button>
          </div>

          <div class="action-card premium" data-action="subscribe" id="nova-subscribe-card">
            <div class="premium-badge">الأفضل</div>
            <div class="action-icon">
              <i class="fas fa-crown"></i>
            </div>
            <h3>اشتراك كامل</h3>
            <p>احصل على اشتراك كامل بأفضل الأسعار</p>
            <div class="action-features">
              <span><i class="fas fa-infinity"></i> بدون حدود زمنية</span>
              <span><i class="fas fa-hd-video"></i> جودة عالية HD</span>
              <span><i class="fas fa-headset"></i> دعم فني 24/7</span>
            </div>
            <button class="action-btn premium" data-action="subscribe">
              <i class="fas fa-star"></i>
              اشترك الآن
            </button>
          </div>
        </div>


        <div id="nova-form-fields" style="display: none;">
          <!-- زر العودة لاختيار نوع الطلب -->
          <button class="back-to-action-btn" onclick="backToActionSelection('nova-form')">
            <i class="fas fa-arrow-right"></i>
            العودة لاختيار نوع الطلب
          </button>

          <input type="text" id="fullname" placeholder="الاسم الرباعي" required />
          <select id="deviceType" required>
            <option value="">اختر نوع الجهاز</option>
            <option value="iphone">📱 آيفون</option>
            <option value="android">🤖 أندرويد</option>
            <option value="computer">💻 كمبيوتر</option>
            <option value="android-tv">📺 شاشة أندرويد</option>
            <option value="samsung-tv">📺 شاشة سامسونج</option>
          </select>
          <!-- قائمة مدة الاشتراك - تظهر فقط عند اختيار الاشتراك في نوفا -->
          <select id="subscriptionDuration" class="subscription-options" style="display: none;">
            <option value="">اختر مدة الاشتراك</option>
            <option value="3 أشهر">اشتراك ثلاثة أشهر</option>
            <option value="6 أشهر">اشتراك ستة أشهر</option>
            <option value="سنة">اشتراك سنة</option>
          </select>

          <!-- مربع عرض السعر - يظهر بعد اختيار مدة الاشتراك -->
          <div id="subscriptionPrice" class="payment-info" style="display: none; margin-bottom: 18px; text-align: center;">
            <p style="font-size: 1.1rem; margin-bottom: 5px;"><strong>سعر الاشتراك:</strong></p>
            <p id="priceDetails" style="font-size: 1.2rem; color: #6afcff;"></p>
          </div>

          <!-- قائمة طريقة الدفع - تظهر فقط عند اختيار الاشتراك في نوفا -->
          <select id="paymentMethod" class="subscription-options" style="display: none;">
            <option value="">اختر طريقة الدفع</option>
            <option value="الكريمي">الكريمي</option>
            <option value="جيب">جيب</option>
            <option value="ون كاش">ون كاش</option>
            <option value="فلوسك">فلوسك</option>
            <option value="تحويل عبر النجم">تحويل عبر النجم أو الشركات المحلية</option>
            <option value="تحويل بنكي عالمي">تحويل بنكي عالمي</option>
          </select>

          <!-- معلومات الدفع - تظهر بناءً على طريقة الدفع المختارة -->
          <div id="paymentInfo" class="payment-info" style="display: none; background: rgba(30, 30, 47, 0.7); padding: 15px; border-radius: 10px; margin-bottom: 18px; border: 1px solid rgba(106, 252, 255, 0.2);">
            <h3 style="color: #6afcff; margin-bottom: 10px; font-size: 1.1rem; text-align: center;">معلومات الدفع</h3>
            <div id="paymentDetails"></div>
          </div>

          <input type="tel" id="clientPhone" placeholder="رقم الجوال (مثلاً 777xxxxxxx)" required />

          <!-- حقل التحقق المدمج -->
          <div class="verification-input-container">
            <input type="text" id="otpCode" placeholder="كود التحقق من واتساب" maxlength="6" disabled />
            <button id="sendOtpBtn" type="button" class="verify-btn">تحقق</button>
          </div>
          <div id="otpStatus" class="otp-status-message"></div>
          <div id="resendTimer" class="resend-timer"></div>

          <button id="requestBtn" disabled>
            <i class="fas fa-rocket"></i> <span id="request-btn-text">طلب الحساب التجريبي</span>
          </button>
          <div class="loader" id="loader1"></div>
          <div class="status" id="status"></div>
          <div class="account-info" id="accountInfo"></div>
          <a href="#" target="_blank" id="whatsappLink" class="whatsapp-link"><i class="fab fa-whatsapp"></i> إرسال البيانات على رقمي واتساب</a>
        </div>
      </div>

      <!-- نموذج نوفا بلص (السيرفر الثاني) -->
      <div class="form-container" id="nova-plus-form">
        <h2>خدمة نوفا Plus</h2>

        <!-- بطاقات اختيار نوع الطلب لنوفا بلص -->
        <div class="action-cards-container">
          <div class="action-card trial-plus" data-action="trial-plus" id="nova-plus-trial-card">
            <div class="trial-badge">تجربة مطولة</div>
            <div class="action-icon">
              <i class="fas fa-stopwatch"></i>
            </div>
            <h3>تجربة مجانية Plus</h3>
            <p>احصل على حساب تجريبي متقدم لمدة 24 ساعة كاملة</p>
            <div class="action-features">
              <span><i class="fas fa-clock"></i> 24 ساعة مجاناً</span>
              <span><i class="fas fa-video"></i> جودة 4K</span>
              <span><i class="fas fa-crown"></i> قنوات حصرية</span>
              <span><i class="fas fa-mobile-alt"></i> جميع الأجهزة</span>
            </div>
            <button class="action-btn trial-plus" onclick="window.location.href='login.html'">
              <i class="fas fa-rocket"></i>
              ابدأ التجربة المتقدمة
            </button>
          </div>

          <div class="action-card premium" data-action="subscribe-plus" id="nova-plus-subscribe-card">
            <div class="premium-badge">الأفضل</div>
            <div class="action-icon">
              <i class="fas fa-gem"></i>
            </div>
            <h3>اشتراك نوفا Plus</h3>
            <p>احصل على الخدمة المتقدمة بجميع المميزات</p>
            <div class="action-features">
              <span><i class="fas fa-crown"></i> خدمة متميزة</span>
              <span><i class="fas fa-video"></i> جودة 4K</span>
              <span><i class="fas fa-shield-alt"></i> أولوية في الدعم</span>
              <span><i class="fas fa-plus-circle"></i> قنوات حصرية</span>
            </div>
            <button class="action-btn premium" data-action="subscribe-plus">
              <i class="fas fa-diamond"></i>
              اشترك في نوفا Plus
            </button>
          </div>
        </div>


        <div id="nova-plus-form-fields" style="display: none;">
          <!-- زر العودة لاختيار نوع الطلب -->
          <button class="back-to-action-btn" onclick="backToActionSelection('nova-plus-form')">
            <i class="fas fa-arrow-right"></i>
            العودة لاختيار نوع الطلب
          </button>

          <input type="text" id="altName" placeholder="اسم صاحب الجهاز" required />
          <select id="deviceTypePlus" required>
            <option value="" disabled selected>اختر نوع الجهاز</option>
            <option value="iphone">📱 آيفون</option>
            <option value="android">🤖 أندرويد</option>
            <option value="computer">💻 كمبيوتر</option>
            <option value="android-tv">📺 شاشة أندرويد</option>
            <option value="samsung-tv">📺 شاشة سامسونج</option>
          </select>
          <select id="subscriptionType" required>
            <option value="">اختر نوع الاشتراك</option>
            <option value="اشتراك ثلاثه اشهر">اشتراك ثلاثه اشهر</option>
            <option value="اشتراك سته اشهر">اشتراك سته اشهر</option>
            <option value="اشتراك سنة">اشتراك سنة</option>
          </select>

          <!-- مربع عرض السعر - يظهر بعد اختيار نوع الاشتراك -->
          <div id="subscriptionPricePlus" class="payment-info" style="display: none; margin-bottom: 18px; text-align: center;">
            <p style="font-size: 1.1rem; margin-bottom: 5px;"><strong>سعر الاشتراك:</strong></p>
            <p id="priceDetailsPlus" style="font-size: 1.2rem; color: #6afcff;"></p>
          </div>

          <!-- قائمة طريقة الدفع - تظهر فقط عند اختيار الاشتراك في نوفا بلص -->
          <select id="paymentMethodPlus" class="subscription-options" style="display: block;">
            <option value="">اختر طريقة الدفع</option>
            <option value="الكريمي">الكريمي</option>
            <option value="جيب">جيب</option>
            <option value="ون كاش">ون كاش</option>
            <option value="فلوسك">فلوسك</option>
            <option value="تحويل عبر النجم او الشركات المحليه">تحويل عبر النجم او الشركات المحليه</option>
            <option value="تحويل بنكي عالمي">تحويل بنكي عالمي</option>
          </select>

          <!-- معلومات الدفع - تظهر بناءً على طريقة الدفع المختارة -->
          <div id="paymentInfoPlus" class="payment-info" style="display: none; background: rgba(30, 30, 47, 0.7); padding: 15px; border-radius: 10px; margin-bottom: 18px; border: 1px solid rgba(106, 252, 255, 0.2);">
            <h3 style="color: #6afcff; margin-bottom: 10px; font-size: 1.1rem; text-align: center;">معلومات الدفع</h3>
            <div id="paymentDetailsPlus"></div>
          </div>

          <input type="tel" id="altPhone" placeholder="رقم للتواصل (مثلاً 777xxxxxxx)" required />

          <!-- حقل التحقق المدمج لنوفا بلص -->
          <div class="verification-input-container">
            <input type="text" id="otpCodePlus" placeholder="كود التحقق من واتساب" maxlength="6" disabled />
            <button id="sendOtpBtnPlus" type="button" class="verify-btn">تحقق</button>
          </div>
          <div id="otpStatusPlus" class="otp-status-message"></div>
          <div id="resendTimerPlus" class="resend-timer"></div>

          <button id="altRequestBtn" disabled>
            <i class="fas fa-rocket"></i> <span id="alt-request-btn-text">طلب الحساب التجريبي</span>
          </button>
          <div class="loader" id="loader2"></div>
          <div class="status" id="altStatus"></div>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <div class="social-links">
        <a href="#"><i class="fab fa-facebook"></i></a>
        <a href="#"><i class="fab fa-telegram"></i></a>
        <a href="#"><i class="fab fa-whatsapp"></i></a>
        <a href="#"><i class="fab fa-instagram"></i></a>
      </div>
      <div class="copyright">
        جميع الحقوق محفوظة &copy; <span id="year">2025</span> نوفا يمن
      </div>
    </div>
  </footer>

  <!-- شاشة التقدم لإنشاء الحساب -->
  <div class="progress-overlay" id="progress-overlay" style="display: none;">
    <div class="progress-container">
      <div class="progress-header">
        <div class="progress-icon">
          <i class="fas fa-cog fa-spin"></i>
        </div>
        <h3 id="progress-title">جاري إنشاء الحساب...</h3>
        <p id="progress-subtitle">الوقت المقدر: دقيقة إلى دقيقتين</p>
      </div>

      <div class="progress-bar-container">
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="progress-percentage" id="progress-percentage">0%</div>
      </div>

      <div class="progress-time">
        <div class="time-remaining">
          <i class="fas fa-clock"></i>
          <span>الوقت المتبقي: </span>
          <span id="time-remaining">--:--</span>
        </div>
        <div class="progress-info" id="progress-info">
          <div class="info-item">
            <i class="fas fa-server"></i>
            <span>الخادم: نشط</span>
          </div>
          <div class="info-item">
            <i class="fas fa-wifi"></i>
            <span id="connection-status">الاتصال: ممتاز</span>
          </div>
        </div>
      </div>

      <div class="progress-steps" id="progress-steps">
        <div class="step active" id="step-1">
          <i class="fas fa-user-plus"></i>
          <span>إنشاء الحساب</span>
        </div>
        <div class="step" id="step-2">
          <i class="fas fa-server"></i>
          <span>تكوين الخادم</span>
        </div>
        <div class="step" id="step-3">
          <i class="fas fa-check-circle"></i>
          <span>اكتمال العملية</span>
        </div>
      </div>
    </div>
  </div>

  <script>
    // متغيرات لحفظ إعدادات الخدمات
    let novaSettings = {};
    let novaPlusSettings = {};

    // تحميل الإعدادات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      loadServiceSettings();
    });

    // تحميل إعدادات الخدمات من قاعدة البيانات
    async function loadServiceSettings() {
      try {
        // تحميل إعدادات نوفا العادية
        const novaResponse = await fetch('api/get-settings.php?service=nova');
        const novaData = await novaResponse.json();
        if (novaData.success) {
          novaSettings = novaData.settings;
        }

        // تحميل إعدادات نوفا بلص
        const novaPlusResponse = await fetch('api/get-settings.php?service=nova-plus');
        const novaPlusData = await novaPlusResponse.json();
        if (novaPlusData.success) {
          novaPlusSettings = novaPlusData.settings;
        }
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        // استخدام القيم الافتراضية في حالة الخطأ
        novaSettings = {
          server_url: 'http://nvpro.tv:80',
          app_download_url: 'https://linkjar.co/NOVA_YEMEN',
          trial_duration: '12',
          whatsapp_number: '967779600073',
          telegram_support: '************'
        };
        novaPlusSettings = {
          server_url: 'http://tayaar.site:2095',
          app_download_url: 'https://linkjar.co/NOVA_YEMEN',
          trial_duration: '24',
          whatsapp_number: '967779600073',
          telegram_support: '************'
        };
      }
    }



    // كشف الأجهزة الضعيفة والإنترنت البطيء
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;
    const isSlowConnection = navigator.connection && (navigator.connection.effectiveType === 'slow-2g' || navigator.connection.effectiveType === '2g');
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    // تطبيق تحسينات للأجهزة الضعيفة
    if (isLowEndDevice || isSlowConnection || prefersReducedMotion) {
      document.body.classList.add('low-performance-mode');

      // تعطيل التأثيرات الثقيلة
      const style = document.createElement('style');
      style.textContent = `
        .low-performance-mode * {
          transition: none !important;
          animation: none !important;
          transform: none !important;
        }
        .low-performance-mode .container:hover,
        .low-performance-mode .service-card:hover,
        .low-performance-mode .action-card:hover {
          transform: none !important;
        }
      `;
      document.head.appendChild(style);
    }

    // معالج الأخطاء العام
    window.addEventListener('error', function(e) {
      console.error('خطأ في الصفحة:', e.error);
    });

    // دالة العودة لاختيار نوع الطلب (دالة عامة)
    function backToActionSelection(formId) {
      try {
        console.log('🔙 العودة لاختيار نوع الطلب في:', formId);

        const form = document.getElementById(formId);
        if (!form) {
          console.error('❌ لم يتم العثور على النموذج:', formId);
          return;
        }

        // إخفاء نماذج الحقول
        const formFields = form.querySelector('[id$="-form-fields"]');
        if (formFields) {
          formFields.style.display = 'none';
          console.log('❌ تم إخفاء نماذج الحقول');
        } else {
          console.warn('⚠️ لم يتم العثور على نماذج الحقول في:', formId);
        }

        // إظهار بطاقات اختيار نوع الطلب
        const actionCardsContainer = form.querySelector('.action-cards-container');
        if (actionCardsContainer) {
          actionCardsContainer.style.display = 'grid';
          console.log('✅ تم إظهار بطاقات اختيار نوع الطلب');
        } else {
          console.warn('⚠️ لم يتم العثور على حاوي البطاقات في:', formId);
        }

        // إزالة التحديد من جميع البطاقات
        const actionCards = form.querySelectorAll('.action-card');
        if (actionCards.length > 0) {
          actionCards.forEach(card => {
            card.classList.remove('active');
          });
          console.log('🔄 تم إعادة تعيين الاختيارات لـ', actionCards.length, 'بطاقة');
        } else {
          console.warn('⚠️ لم يتم العثور على بطاقات في:', formId);
        }

        console.log('✅ تم إكمال العودة لاختيار نوع الطلب');

      } catch (error) {
        console.error('❌ خطأ في دالة العودة:', error);
      }
    }

    // دالة للعودة إلى اختيار الخدمات (دالة عامة)
    function backToServiceSelection() {
      console.log('🏠 العودة لاختيار الخدمات');

      // إخفاء جميع النماذج
      const novaForm = document.getElementById('nova-form');
      const novaPlusForm = document.getElementById('nova-plus-form');

      if (novaForm) novaForm.classList.remove('active');
      if (novaPlusForm) novaPlusForm.classList.remove('active');

      // إظهار بطاقات الخدمات والعنوان
      const serviceCardsContainer = document.querySelector('.service-cards-container');
      const pageHeader = document.querySelector('.page-header');

      if (serviceCardsContainer) {
        serviceCardsContainer.style.display = 'grid';
      }
      if (pageHeader) {
        pageHeader.style.display = 'block';
      }

      // إزالة التحديد من جميع بطاقات الخدمات
      const serviceCards = document.querySelectorAll('.service-card');
      serviceCards.forEach(card => {
        card.classList.remove('active');
      });

      console.log('✅ تم العودة لاختيار الخدمات');
    }

    // دوال شاشة التقدم
    let progressInterval;
    let timeInterval;
    let currentProgress = 0;
    let totalTime = 0;
    let remainingTime = 0;

    // متغيرات لحفظ نوع الطلب المحدد
    let selectedNovaActionType = 'trial';
    let selectedNovaPlusActionType = 'trial-plus';

    function showProgressScreen() {
      console.log('🚀 بدء عرض شاشة التقدم - محاكاة السيرفر');

      // إظهار شاشة التقدم
      const progressOverlay = document.getElementById('progress-overlay');
      progressOverlay.style.display = 'flex';

      // تقدير أولي للوقت (30-90 ثانية)
      totalTime = Math.floor(Math.random() * 61) + 30; // 30-90 ثانية
      remainingTime = totalTime;
      currentProgress = 0;

      console.log('⏱️ الوقت المقدر الأولي:', totalTime, 'ثانية');

      // تحديث الوقت المتبقي
      updateTimeDisplay();

      // تحديث معلومات الاتصال
      updateConnectionInfo();

      // بدء محاكاة التقدم (بدون تايمر ثابت)
      startServerSimulation();

      // تحديث الخطوات
      updateSteps();
    }

    function updateConnectionInfo() {
      const connectionStatus = document.getElementById('connection-status');
      if (connectionStatus) {
        const speeds = ['ممتاز', 'جيد جداً', 'جيد', 'متوسط'];
        const randomSpeed = speeds[Math.floor(Math.random() * speeds.length)];
        connectionStatus.textContent = `الاتصال: ${randomSpeed}`;

        // تحديث لون المؤشر حسب السرعة
        const icon = connectionStatus.parentElement.querySelector('i');
        if (icon) {
          icon.style.color = randomSpeed === 'ممتاز' ? '#50C878' :
                           randomSpeed === 'جيد جداً' ? '#4A90E2' :
                           randomSpeed === 'جيد' ? '#FFD700' : '#ff6b6b';
        }
      }
    }

    function startServerSimulation() {
      const progressFill = document.getElementById('progress-fill');
      const progressPercentage = document.getElementById('progress-percentage');

      function updateProgress() {
        if (currentProgress >= 100) {
          clearInterval(progressInterval);
          return;
        }

        // تقدم عشوائي يحاكي السيرفر (أحياناً سريع، أحياناً بطيء)
        let increment;

        if (currentProgress < 20) {
          // البداية سريعة (إعداد الطلب)
          increment = Math.random() * 6 + 2; // 2-8%
        } else if (currentProgress < 60) {
          // الوسط متغير (إنشاء الحساب)
          increment = Math.random() * 3 + 1; // 1-4%
        } else if (currentProgress < 90) {
          // النهاية بطيئة (تكوين الخادم)
          increment = Math.random() * 1.5 + 0.5; // 0.5-2%
        } else {
          // اللمسة الأخيرة - بطيء جداً
          increment = Math.random() * 0.8 + 0.2; // 0.2-1%
        }

        currentProgress += increment;

        // تحديث الوقت المتبقي بناءً على التقدم
        const estimatedRemaining = Math.max(0, Math.floor((100 - currentProgress) * 0.6));
        remainingTime = estimatedRemaining;
        updateTimeDisplay();

        // تحديث شريط التقدم
        progressFill.style.width = Math.min(currentProgress, 100) + '%';
        progressPercentage.textContent = Math.floor(Math.min(currentProgress, 100)) + '%';

        // جدولة التحديث التالي بفترة عشوائية (محاكاة السيرفر)
        const nextUpdateDelay = Math.random() * 2000 + 800; // 0.8-2.8 ثانية
        progressInterval = setTimeout(updateProgress, nextUpdateDelay);
      }

      // بدء التحديث الأول
      updateProgress();
    }

    function startTimeCounter() {
      timeInterval = setInterval(() => {
        remainingTime--;

        if (remainingTime <= 0) {
          remainingTime = 0;
          clearInterval(timeInterval);
        }

        updateTimeDisplay();
      }, 1000);
    }

    function updateTimeDisplay() {
      const timeRemainingElement = document.getElementById('time-remaining');
      const minutes = Math.floor(remainingTime / 60);
      const seconds = remainingTime % 60;

      timeRemainingElement.textContent =
        `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    function updateSteps() {
      const step1 = document.getElementById('step-1');
      const step2 = document.getElementById('step-2');
      const step3 = document.getElementById('step-3');
      const progressTitle = document.getElementById('progress-title');

      // إعادة تعيين الخطوات
      [step1, step2, step3].forEach(step => {
        step.classList.remove('active', 'completed');
      });

      // الخطوة الأولى نشطة من البداية
      step1.classList.add('active');

      // تحديث الخطوات حسب التقدم مع رسائل
      setTimeout(() => {
        step1.classList.remove('active');
        step1.classList.add('completed');
        step2.classList.add('active');
        progressTitle.textContent = 'جاري تكوين الخادم...';
      }, totalTime * 0.3 * 1000); // 30% من الوقت

      setTimeout(() => {
        step2.classList.remove('active');
        step2.classList.add('completed');
        step3.classList.add('active');
        progressTitle.textContent = 'جاري إنهاء العملية...';
      }, totalTime * 0.7 * 1000); // 70% من الوقت
    }

    function completeProgress() {
      console.log('✅ تم إكمال إنشاء الحساب');

      // تحديث النصوص
      const progressTitle = document.getElementById('progress-title');
      const progressSubtitle = document.getElementById('progress-subtitle');
      const progressIcon = document.querySelector('.progress-icon');
      const progressContainer = document.querySelector('.progress-container');
      const progressInfo = document.getElementById('progress-info');

      progressTitle.textContent = '🎉 تم إنشاء الحساب بنجاح!';
      progressSubtitle.textContent = 'يمكنك الآن استخدام خدماتنا والاستمتاع بالمحتوى';

      // تغيير الأيقونة مع تأثير
      progressIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
      progressIcon.classList.add('success');

      // إضافة تأثير نجاح للحاوي
      progressContainer.style.borderColor = 'rgba(80, 200, 120, 0.6)';
      progressContainer.style.boxShadow = `
        0 25px 50px rgba(0, 0, 0, 0.6),
        0 0 30px rgba(80, 200, 120, 0.4),
        0 0 60px rgba(80, 200, 120, 0.2)
      `;

      // إكمال جميع الخطوات
      const step3 = document.getElementById('step-3');
      step3.classList.remove('active');
      step3.classList.add('completed');

      // تحديث معلومات الحالة
      if (progressInfo) {
        progressInfo.innerHTML = `
          <div class="info-item success">
            <i class="fas fa-check-circle"></i>
            <span>تم بنجاح!</span>
          </div>
          <div class="info-item success">
            <i class="fas fa-rocket"></i>
            <span>جاهز للاستخدام</span>
          </div>
        `;
      }

      // إضافة تأثير احتفالي
      createCelebrationEffect();

      // إخفاء شاشة التقدم بعد 4 ثوان
      setTimeout(() => {
        hideProgressScreen();
      }, 4000);
    }

    function createCelebrationEffect() {
      // إضافة تأثير احتفالي بسيط
      const progressContainer = document.querySelector('.progress-container');
      if (progressContainer) {
        progressContainer.style.animation = 'celebrationPulse 0.6s ease';

        setTimeout(() => {
          progressContainer.style.animation = '';
        }, 600);
      }
    }

    function hideProgressScreen() {
      const progressOverlay = document.getElementById('progress-overlay');
      progressOverlay.style.display = 'none';

      // تنظيف المتغيرات
      clearTimeout(progressInterval);
      clearInterval(timeInterval);
      currentProgress = 0;

      // إعادة تعيين شاشة التقدم للاستخدام التالي
      resetProgressScreen();

      console.log('🏠 تم إخفاء شاشة التقدم - البيانات ستظهر في الصفحة');

      // التمرير إلى البيانات تلقائياً
      setTimeout(() => {
        const accountInfo = document.getElementById('accountInfo');
        const altAccountInfo = document.getElementById('altAccountInfo');

        if (accountInfo && accountInfo.style.display !== 'none') {
          // إضافة تأثير بصري لجذب الانتباه
          accountInfo.style.animation = 'highlightData 2s ease';
          accountInfo.scrollIntoView({ behavior: 'smooth', block: 'center' });
          console.log('📋 تم التمرير إلى بيانات نوفا');
        } else if (altAccountInfo && altAccountInfo.style.display !== 'none') {
          // إضافة تأثير بصري لجذب الانتباه
          altAccountInfo.style.animation = 'highlightData 2s ease';
          altAccountInfo.scrollIntoView({ behavior: 'smooth', block: 'center' });
          console.log('📋 تم التمرير إلى بيانات نوفا بلص');
        }
      }, 500); // انتظار نصف ثانية للتأكد من ظهور البيانات
    }

    function resetProgressScreen() {
      // إعادة تعيين النصوص
      const progressTitle = document.getElementById('progress-title');
      const progressSubtitle = document.getElementById('progress-subtitle');
      const progressIcon = document.querySelector('.progress-icon');
      const progressContainer = document.querySelector('.progress-container');
      const progressFill = document.getElementById('progress-fill');
      const progressPercentage = document.getElementById('progress-percentage');
      const progressTime = document.querySelector('.progress-time');

      progressTitle.textContent = 'جاري إنشاء الحساب...';
      progressSubtitle.textContent = 'الوقت المقدر: دقيقة إلى دقيقتين';

      // إعادة تعيين الأيقونة
      progressIcon.innerHTML = '<i class="fas fa-cog fa-spin"></i>';
      progressIcon.classList.remove('success');

      // إعادة تعيين التصميم
      progressContainer.style.borderColor = 'rgba(74, 144, 226, 0.3)';
      progressContainer.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.5)';

      // إعادة تعيين شريط التقدم
      progressFill.style.width = '0%';
      progressPercentage.textContent = '0%';

      // إظهار الوقت المتبقي
      progressTime.style.display = 'block';

      // إعادة تعيين الخطوات
      const steps = document.querySelectorAll('.step');
      steps.forEach(step => {
        step.classList.remove('active', 'completed');
      });
      document.getElementById('step-1').classList.add('active');
    }

    // دالة لإكمال التقدم عند انتهاء السيرفر
    function completeProgressFromServer(success = true) {
      console.log('🎯 السيرفر انتهى - إكمال التقدم:', success ? 'نجح' : 'فشل');

      // إيقاف المحاكاة
      clearTimeout(progressInterval);
      clearInterval(timeInterval);

      // إكمال التقدم إلى 100%
      const progressFill = document.getElementById('progress-fill');
      const progressPercentage = document.getElementById('progress-percentage');

      currentProgress = 100;
      remainingTime = 0;

      progressFill.style.width = '100%';
      progressPercentage.textContent = '100%';
      updateTimeDisplay();

      // إكمال العملية
      if (success) {
        completeProgress();
      } else {
        showProgressError();
      }
    }

    // دالة لإظهار خطأ في التقدم
    function showProgressError() {
      console.log('❌ حدث خطأ في إنشاء الحساب');

      const progressTitle = document.getElementById('progress-title');
      const progressSubtitle = document.getElementById('progress-subtitle');
      const progressIcon = document.querySelector('.progress-icon');
      const progressContainer = document.querySelector('.progress-container');

      progressTitle.textContent = 'حدث خطأ في إنشاء الحساب';
      progressSubtitle.textContent = 'يرجى المحاولة مرة أخرى';

      // تغيير الأيقونة للخطأ
      progressIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
      progressIcon.classList.add('error');

      // تغيير لون الحاوي للخطأ
      progressContainer.style.borderColor = 'var(--error-color)';
      progressContainer.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.5), 0 0 20px rgba(220, 53, 69, 0.3)';

      // إخفاء بعد 3 ثوان
      setTimeout(() => {
        hideProgressScreen();
      }, 3000);
    }

    // التفاعل مع البطاقات التفاعلية
    document.addEventListener('DOMContentLoaded', function() {
      try {
        const serviceCards = document.querySelectorAll('.service-card');
        const selectButtons = document.querySelectorAll('.select-service-btn');
        const novaForm = document.getElementById('nova-form');
        const novaPlusForm = document.getElementById('nova-plus-form');

        console.log('تم تحميل العناصر:', {
          serviceCards: serviceCards.length,
          selectButtons: selectButtons.length,
          novaForm: !!novaForm,
          novaPlusForm: !!novaPlusForm
        });

      // إضافة مستمعي الأحداث للبطاقات
      serviceCards.forEach(card => {
        card.addEventListener('click', function() {
          const serviceType = this.dataset.service;
          selectService(serviceType);
        });
      });

      // إضافة مستمعي الأحداث للأزرار
      selectButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          e.stopPropagation();
          const serviceType = this.dataset.service;
          selectService(serviceType);
        });
      });



      function selectService(serviceType) {
        console.log('🎯 المستخدم اختار الخدمة:', serviceType);

        // إزالة التحديد من جميع بطاقات الخدمات
        for (let i = 0; i < serviceCards.length; i++) {
          serviceCards[i].classList.remove('active');
        }

        // تحديد البطاقة المختارة
        const selectedCard = document.querySelector(`[data-service="${serviceType}"]`);
        if (selectedCard) {
          selectedCard.classList.add('active');
          console.log('✅ تم تحديد بطاقة الخدمة:', serviceType);
        }



        // إخفاء بطاقات الخدمات وإظهار النموذج المختار
        const serviceCardsContainer = document.querySelector('.service-cards-container');
        const pageHeader = document.querySelector('.page-header');

        if (serviceCardsContainer) {
          serviceCardsContainer.style.display = 'none';
        }
        if (pageHeader) {
          pageHeader.style.display = 'none';
        }

        // إخفاء جميع النماذج أولاً
        novaForm.classList.remove('active');
        novaPlusForm.classList.remove('active');
        console.log('🔄 تم إخفاء بطاقات الخدمات وجميع النماذج');

        // إظهار النموذج المناسب للخدمة المختارة
        if (serviceType === 'nova') {
          novaForm.classList.add('active');
          console.log('📋 تم إظهار خيارات نوفا - المستخدم يختار نوع الطلب');

        } else if (serviceType === 'nova-plus') {
          novaPlusForm.classList.add('active');
          console.log('📋 تم إظهار خيارات نوفا بلص - المستخدم يختار نوع الطلب');
        }

        // تأثير بصري للأجهزة القوية فقط
        if (!isLowEndDevice && !prefersReducedMotion && selectedCard) {
          selectedCard.style.transform = 'scale(1.05)';
          setTimeout(() => {
            selectedCard.style.transform = '';
          }, 200);
        }
      }



      // التفاعل مع بطاقات اختيار نوع الطلب
      const actionCards = document.querySelectorAll('.action-card');
      const actionButtons = document.querySelectorAll('.action-btn');

      // إضافة مستمعي الأحداث لبطاقات الأعمال
      actionCards.forEach(card => {
        card.addEventListener('click', function() {
          const actionType = this.dataset.action;
          const parentForm = this.closest('.form-container');
          selectActionType(actionType, parentForm);
        });
      });

      // إضافة مستمعي الأحداث لأزرار الأعمال
      actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          e.stopPropagation();
          e.preventDefault();

          const actionType = this.dataset.action;
          const parentForm = this.closest('.form-container');

          console.log('تم الضغط على زر:', actionType, 'في النموذج:', parentForm.id);

          // تحديد نوع الطلب أولاً
          selectActionType(actionType, parentForm);

          // انتظار قصير للتأكد من تحديث النموذج
          setTimeout(() => {
            // تشغيل الزر المناسب حسب النموذج
            if (parentForm.id === 'nova-form') {
              const requestBtn = document.getElementById('requestBtn');
              console.log('محاولة تشغيل زر نوفا:', requestBtn);
              if (requestBtn) {
                requestBtn.click();
              }
            } else if (parentForm.id === 'nova-plus-form') {
              const altRequestBtn = document.getElementById('altRequestBtn');
              console.log('محاولة تشغيل زر نوفا بلص:', altRequestBtn);
              if (altRequestBtn) {
                altRequestBtn.click();
              }
            }
          }, 100);
        });
      });

      function selectActionType(actionType, parentForm) {
        console.log('🎯 المستخدم اختار نوع الطلب:', actionType);

        // حفظ نوع الطلب في المتغير المناسب
        if (parentForm.id === 'nova-form') {
          selectedNovaActionType = actionType;
          console.log('💾 تم حفظ نوع طلب نوفا:', actionType);
        } else if (parentForm.id === 'nova-plus-form') {
          selectedNovaPlusActionType = actionType;
          console.log('💾 تم حفظ نوع طلب نوفا بلص:', actionType);
        }

        // إزالة التحديد من جميع البطاقات أولاً
        const allActionCards = parentForm.querySelectorAll('.action-card');
        allActionCards.forEach(card => {
          card.classList.remove('active');
        });

        // تحديد البطاقة المختارة
        const selectedCard = parentForm.querySelector(`[data-action="${actionType}"]`);
        if (selectedCard) {
          selectedCard.classList.add('active');
          console.log('✅ تم تحديد البطاقة:', actionType);
        }

        // إخفاء جميع بطاقات الاختيار في هذا النموذج
        const actionCardsContainer = parentForm.querySelector('.action-cards-container');
        if (actionCardsContainer) {
          actionCardsContainer.style.display = 'none';
          console.log('❌ تم إخفاء بطاقات الاختيار');
        }

        // إظهار النموذج المناسب للخيار المحدد
        showFormFields(actionType, parentForm);

        console.log('✅ تم إظهار نموذج:', actionType);
      }





      // تحسين إمكانية الوصول للبطاقات
      actionCards.forEach(card => {
        card.setAttribute('role', 'button');
        card.setAttribute('tabindex', '0');

        // دعم التنقل بالكيبورد
        card.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
          }
        });
      });

      // تحسين إمكانية الوصول للأزرار
      actionButtons.forEach(button => {
        button.setAttribute('role', 'button');
        button.setAttribute('tabindex', '0');

        // دعم التنقل بالكيبورد
        button.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
          }
        });
      });

      function showFormFields(actionType, parentForm) {
        const formFields = parentForm.querySelector('[id$="-form-fields"]');
        const requestBtnText = parentForm.querySelector('#request-btn-text, #alt-request-btn-text');

        if (formFields) {
          formFields.style.display = 'block';
        }

        // تحديث نص الزر حسب نوع الطلب
        if (requestBtnText) {
          let newButtonText = '';
          switch(actionType) {
            case 'trial':
              newButtonText = 'طلب الحساب التجريبي';
              break;
            case 'subscribe':
              newButtonText = 'اشترك الان';
              break;
            case 'trial-plus':
              newButtonText = 'طلب الحساب التجريبي';
              break;
            case 'subscribe-plus':
              newButtonText = 'اشترك في نوفا Plus';
              break;
          }

          requestBtnText.textContent = newButtonText;
        }

        // إظهار/إخفاء الحقول المناسبة حسب نوع الطلب
        const subscriptionDuration = parentForm.querySelector('#subscriptionDuration');
        const subscriptionType = parentForm.querySelector('#subscriptionType');
        const paymentMethod = parentForm.querySelector('#paymentMethod, #paymentMethodPlus');
        const paymentInfo = parentForm.querySelector('#paymentInfo, #paymentInfoPlus');
        const subscriptionPrice = parentForm.querySelector('#subscriptionPrice, #subscriptionPricePlus');

        if (actionType === 'trial' || actionType === 'trial-plus') {
          // للحساب التجريبي - إخفاء خيارات الاشتراك
          if (subscriptionDuration) subscriptionDuration.style.display = 'none';
          if (subscriptionType) subscriptionType.style.display = 'none';
          if (paymentMethod) paymentMethod.style.display = 'none';
          if (paymentInfo) paymentInfo.style.display = 'none';
          if (subscriptionPrice) subscriptionPrice.style.display = 'none';
        } else {
          // للاشتراك - إظهار خيارات الاشتراك
          if (subscriptionDuration) subscriptionDuration.style.display = 'block';
          if (subscriptionType) subscriptionType.style.display = 'block';
          if (paymentMethod) paymentMethod.style.display = 'block';
        }
      }

      // تحسينات إضافية للتفاعل

      // إضافة تأثيرات صوتية (اختيارية)
      function playClickSound() {
        // يمكن إضافة صوت نقر هنا إذا رغبت
        // const audio = new Audio('click-sound.mp3');
        // audio.play();
      }

      // تحسين تجربة اللمس للأجهزة المحمولة
      actionCards.forEach(card => {
        card.addEventListener('touchstart', function() {
          this.style.transform = 'scale(0.98)';
        });

        card.addEventListener('touchend', function() {
          this.style.transform = '';
        });
      });

      // إضافة مؤشرات بصرية للحالة
      function updateCardStates() {
        actionCards.forEach(card => {
          const isActive = card.classList.contains('active');
          const icon = card.querySelector('.action-icon i');

          if (isActive && icon) {
            icon.style.animation = 'bounce 0.6s ease';
            setTimeout(() => {
              icon.style.animation = '';
            }, 600);
          }
        });
      }

      // مراقبة تغييرات الحالة
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            updateCardStates();
          }
        });
      });

      // بدء مراقبة البطاقات
      actionCards.forEach(card => {
        observer.observe(card, { attributes: true });
      });

      // تحسين إمكانية الوصول (Accessibility)
      actionCards.forEach(card => {
        card.setAttribute('role', 'button');
        card.setAttribute('tabindex', '0');

        // دعم التنقل بالكيبورد
        card.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
          }
        });
      });

      // إضافة تأثير التحميل
      function showLoadingState(card) {
        card.classList.add('loading');
        const button = card.querySelector('.action-btn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';

        setTimeout(() => {
          card.classList.remove('loading');
          button.innerHTML = originalText;
        }, 1000);
      }

      // تطبيق تأثير التحميل عند النقر
      actionButtons.forEach(button => {
        button.addEventListener('click', function() {
          const card = this.closest('.action-card');
          showLoadingState(card);
        });
      });

        // التأكد من وجود الأزرار الأصلية
        const requestBtn = document.getElementById('requestBtn');
        const altRequestBtn = document.getElementById('altRequestBtn');

        if (!requestBtn || !altRequestBtn) {
          console.error('الأزرار الأصلية غير موجودة');
        } else {
          console.log('تم العثور على الأزرار الأصلية');
        }

        // لا نحدد أي خدمة افتراضياً - فقط بطاقات الاختيار تظهر
        console.log('✅ تم تحميل الصفحة بنجاح - بطاقات اختيار الخدمة ظاهرة');

      } catch (error) {
        console.error('خطأ في تحميل الصفحة:', error);
        // إظهار رسالة خطأ للمستخدم
        document.body.innerHTML = `
          <div style="text-align: center; padding: 50px; color: #fff; background: #1a1d23; min-height: 100vh;">
            <h2>عذراً، حدث خطأ في تحميل الصفحة</h2>
            <p>يرجى إعادة تحميل الصفحة أو المحاولة لاحقاً</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #4a90e2; color: white; border: none; border-radius: 5px; cursor: pointer;">
              إعادة تحميل
            </button>
          </div>
        `;
      }
    });



    // التعامل مع اختيار مدة الاشتراك وعرض السعر المناسب
    document.getElementById('subscriptionDuration').addEventListener('change', function() {
      const subscriptionPrice = document.getElementById('subscriptionPrice');
      const priceDetails = document.getElementById('priceDetails');

      // إظهار مربع السعر
      subscriptionPrice.style.display = 'block';

      // تحديد السعر بناءً على مدة الاشتراك المختارة
      switch(this.value) {
        case '3 أشهر':
          priceDetails.innerHTML = '4300 يمني قديم أو 31﷼ سعودي';
          break;
        case '6 أشهر':
          priceDetails.innerHTML = '5900 يمني قديم أو 43﷼ سعودي';
          break;
        case 'سنة':
          priceDetails.innerHTML = '9900 يمني قديم أو 70﷼ سعودي';
          break;
        default:
          subscriptionPrice.style.display = 'none';
          break;
      }
    });

    // التعامل مع اختيار طريقة الدفع وعرض معلومات الدفع المناسبة
    document.getElementById('paymentMethod').addEventListener('change', function() {
      const paymentInfo = document.getElementById('paymentInfo');
      const paymentDetails = document.getElementById('paymentDetails');

      // إظهار خانة معلومات الدفع
      paymentInfo.style.display = 'block';

      // تحديد معلومات الدفع بناءً على طريقة الدفع المختارة
      switch(this.value) {
        case 'الكريمي':
          paymentDetails.innerHTML = `
            <p><strong>▪️حســـاب الريــال اليمني:</strong> 3040261426</p>
            <p><strong>▪️حســـاب الــدولار الإمــريكي:</strong> 3064790144</p>
            <p><strong>▪️حســـاب الريـــال الســعودي:</strong> 3046001817</p>
            <p><strong>*|||  بإسم:</strong> [ وجدان حلمي عبد العزيز السروري ]*</p>
          `;
          break;
        case 'جيب':
          paymentDetails.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>*|||  بإسم:</strong> [ وجدان حلمي عبد العزيز السروري ]*</p>
          `;
          break;
        case 'ون كاش':
          paymentDetails.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>*|||  بإسم:</strong> [ وجدان حلمي عبد العزيز السروري ]*</p>
          `;
          break;
        case 'فلوسك':
          paymentDetails.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>*|||  بإسم:</strong> [ وجدان حلمي عبد العزيز السروري ]*</p>
          `;
          break;
        case 'تحويل عبر النجم':
          paymentDetails.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>*|||  بإسم:</strong> [ وجدان حلمي عبد العزيز السروري ]*</p>
          `;
          break;
        case 'تحويل بنكي عالمي':
          paymentDetails.innerHTML = `
            <p>التفاصيل في الواتساب</p>
            <p>سيتم التواصل معك لإتمام عملية التحويل</p>
          `;
          break;
        default:
          paymentInfo.style.display = 'none';
          break;
      }
    });

    // التعامل مع اختيار نوع الاشتراك في نوفا بلص وعرض السعر المناسب
    document.getElementById('subscriptionType').addEventListener('change', function() {
      const subscriptionPricePlus = document.getElementById('subscriptionPricePlus');
      const priceDetailsPlus = document.getElementById('priceDetailsPlus');

      // إظهار مربع السعر
      subscriptionPricePlus.style.display = 'block';

      // تحديد السعر بناءً على نوع الاشتراك المختار
      switch(this.value) {
        case 'اشتراك ثلاثه اشهر':
          priceDetailsPlus.innerHTML = '4300 يمني قديم أو 31﷼ سعودي';
          break;
        case 'اشتراك سته اشهر':
          priceDetailsPlus.innerHTML = '5900 يمني قديم أو 43﷼ سعودي';
          break;
        case 'اشتراك سنة':
          priceDetailsPlus.innerHTML = '9900 يمني قديم أو 70﷼ سعودي';
          break;
        default:
          subscriptionPricePlus.style.display = 'none';
          break;
      }
    });

    // التعامل مع اختيار طريقة الدفع في نوفا بلص
    document.getElementById('paymentMethodPlus').addEventListener('change', function() {
      const paymentInfoPlus = document.getElementById('paymentInfoPlus');
      const paymentDetailsPlus = document.getElementById('paymentDetailsPlus');

      // إظهار خانة معلومات الدفع
      paymentInfoPlus.style.display = 'block';

      // تحديد معلومات الدفع بناءً على طريقة الدفع المختارة
      switch(this.value) {
        case 'الكريمي':
          paymentDetailsPlus.innerHTML = `
            <p><strong>حســـاب الريــال اليمني:</strong> 3040261426</p>
            <p><strong>حســـاب الــدولار الإمــريكي:</strong> 3064790144</p>
            <p><strong>حســـاب الريـــال الســعودي:</strong> 3046001817</p>
            <p><strong>بإسم:</strong> وجدان حلمي عبد العزيز السروري</p>
          `;
          break;
        case 'جيب':
          paymentDetailsPlus.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>بإسم:</strong> وجدان حلمي عبد العزيز السروري</p>
          `;
          break;
        case 'ون كاش':
          paymentDetailsPlus.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>بإسم:</strong> وجدان حلمي عبد العزيز السروري</p>
          `;
          break;
        case 'فلوسك':
          paymentDetailsPlus.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>بإسم:</strong> وجدان حلمي عبد العزيز السروري</p>
          `;
          break;
        case 'تحويل عبر النجم او الشركات المحليه':
          paymentDetailsPlus.innerHTML = `
            <p><strong>الرقم:</strong> 779600073</p>
            <p><strong>بإسم:</strong> وجدان حلمي عبد العزيز السروري</p>
          `;
          break;
        case 'تحويل بنكي عالمي':
          paymentDetailsPlus.innerHTML = `
            <p><strong>التفاصيل:</strong> يرجى التواصل عبر الواتساب للحصول على تفاصيل التحويل البنكي العالمي</p>
          `;
          break;
        default:
          paymentInfoPlus.style.display = 'none';
          break;
      }
    });

    function generateUsername(length = 8) {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
      let username = '';
      for(let i = 0; i < length; i++) {
        username += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return username;
    }

    // دالة لتوليد سلسلة عشوائية من الأرقام فقط بطول 15 رقم
    function generateRandom15Digits() {
      const digits = '0123456789';
      let result = '';
      for(let i = 0; i < 15; i++) {
        result += digits.charAt(Math.floor(Math.random() * digits.length));
      }
      return result;
    }

    document.getElementById('requestBtn').addEventListener('click', async () => {
      const fullname = document.getElementById('fullname').value.trim();
      const deviceType = document.getElementById('deviceType').value.trim();
      const subscriptionDuration = document.getElementById('subscriptionDuration').value.trim();
      const paymentMethod = document.getElementById('paymentMethod').value.trim();
      const clientPhone = document.getElementById('clientPhone').value.trim();
      const statusDiv = document.getElementById('status');
      const whatsappLink = document.getElementById('whatsappLink');
      const accountInfo = document.getElementById('accountInfo');
      const loader = document.getElementById('loader1');
      // استخدام نوع الطلب المحفوظ
      const actionType = selectedNovaActionType;

      console.log('🔍 تفاصيل الطلب:', {
        actionType,
        fullname,
        deviceType,
        subscriptionDuration,
        paymentMethod,
        clientPhone
      });

      statusDiv.textContent = "";
      whatsappLink.style.display = 'none'; // اخفي الرابط افتراضياً
      accountInfo.style.display = 'none';

      // تحقق من القيم المطلوبة بناءً على نوع الطلب
      if (actionType === 'subscribe') {
        // للاشتراك نحتاج التحقق من مدة الاشتراك وطريقة الدفع
        if (!clientPhone.match(/^\d{8,15}$/) || !fullname || !deviceType || !subscriptionDuration || !paymentMethod) {
          statusDiv.textContent = "❌ تأكد من إدخال جميع الحقول بشكل صحيح.";
          return;
        }

        // إرسال طلب الاشتراك عبر الواتساب مباشرة
        let whatsappMessage = `*🔥 طلب اشتراك في نوفا*%0A%0A`;
        whatsappMessage += `👤 *الاسم:* ${fullname}%0A`;
        whatsappMessage += `📱 *نوع الجهاز:* ${deviceType}%0A`;
        whatsappMessage += `☎️ *رقم الجوال:* ${clientPhone}%0A`;
        whatsappMessage += `📆 *مدة الاشتراك:* ${subscriptionDuration}%0A`;
        whatsappMessage += `💳 *طريقة الدفع:* ${paymentMethod}%0A%0A`;
        whatsappMessage += `✅ *يرجى تأكيد الطلب وإرسال تفاصيل الدفع*`;

        // فتح الواتساب مع الرسالة
        window.open(`https://wa.me/967779600073?text=${whatsappMessage}`, '_blank');

        // عرض رسالة للمستخدم
        statusDiv.textContent = "✅ تم إرسال طلبك بنجاح. سيتم التواصل معك قريباً.";

        // إعادة تفعيل الزر للاشتراكات
        const requestBtn = document.getElementById('requestBtn');
        if (requestBtn) {
          requestBtn.disabled = false;
        }

      } else {
        // للحساب التجريبي نستمر بالطريقة القديمة
        if (!clientPhone.match(/^\d{8,15}$/) || !fullname || !deviceType) {
          statusDiv.textContent = "❌ تأكد من إدخال جميع الحقول بشكل صحيح.";
          return;
        }

        const username = generateUsername();
        const password = '779600073';

        // إظهار شاشة التقدم بعد التحقق من البيانات
        showProgressScreen();

        statusDiv.textContent = '⏳ جاري إنشاء الحساب ...';
        loader.style.display = 'block';
        const requestBtn = document.getElementById('requestBtn');
        requestBtn.disabled = true;

        try {
          const res = await fetch('proxy.php?path=create-trial', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              fullname,
              deviceType,
              clientPhone,
              subscriptionDuration,
              paymentMethod,
              username,
              password
            })
          });

          const data = await res.json();
          loader.style.display = 'none';

          // إكمال شاشة التقدم بناءً على نتيجة السيرفر
          completeProgressFromServer(data.success);

          if (data.success) {
            statusDiv.textContent = `✅ تم إنشاء الحساب بنجاح!`;

            // جلب رابط التطبيق المناسب للجهاز
            const appUrl = getDeviceAppUrl(novaSettings, deviceType);
            const trialDuration = novaSettings.trial_duration || '12';

            // جلب جميع الخوادم المتاحة
            const servers = getAllServers(novaSettings);
            const serversHTML = generateServersHTML(servers);

            // عرض معلومات الحساب مع رسالة نجاح
            accountInfo.innerHTML = `
              <div class="success-header">
                <i class="fas fa-check-circle"></i>
                <h3>تم إنشاء حسابك التجريبي بنجاح!</h3>
                <p>يمكنك الآن استخدام البيانات التالية للدخول:</p>
              </div>
              <p><strong>الاسم:</strong> ${fullname}</p>
              <p><strong>نوع الجهاز:</strong> ${getDeviceName(deviceType)}</p>
              <p><strong>رقم الجوال:</strong> ${clientPhone}</p>
              <p><strong>اسم المستخدم:</strong> ${username}</p>
              <p><strong>كلمة المرور:</strong> ${password}</p>
              <p><strong>مدة التجربة:</strong> ${trialDuration} ساعة</p>
              ${serversHTML}
              <div style="margin: 15px 0;">
                <strong>رابط تحميل التطبيق:</strong><br>
                <a href="${appUrl}" target="_blank" style="color: #6afcff; word-break: break-all;">${appUrl}</a>
                <button onclick="copyServerUrl('${appUrl}')"
                        style="margin-right: 10px; padding: 6px 12px; background: #6afcff; color: #1e1e2f; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                  نسخ
                </button>
              </div>
              ${subscriptionDuration ? `<p><strong>مدة الاشتراك:</strong> ${subscriptionDuration}</p>` : ''}
              ${paymentMethod ? `<p><strong>طريقة الدفع:</strong> ${paymentMethod}</p>` : ''}
            `;
            accountInfo.style.display = 'block';

            // إنشاء قائمة الخوادم للرسالة
            let serversText = '';
            if (servers.length === 1) {
              serversText = `🌐 الخادم: ${servers[0].name}\n🔗 الرابط: ${servers[0].url}`;
            } else {
              serversText = '🌐 الخوادم المتاحة:\n';
              servers.forEach((server, index) => {
                serversText += `${index + 1}. ${server.name}: ${server.url}\n`;
              });
            }

            const msg = `تم إنشاء حسابك التجريبي بنجاح!

يمكنك الآن استخدام البيانات التالية للدخول:

الاسم: ${fullname}

نوع الجهاز: ${getDeviceName(deviceType)}

رقم الجوال: ${clientPhone}

اسم المستخدم: ${username}

كلمة المرور: ${password}

نوع الحساب: حساب تجريبي نوفا ${trialDuration} ساعة

الخادم: ${servers.length > 0 ? servers[0].url : 'nvpro.tv:80'}

رابط تحميل التطبيق:
${appUrl}`;

            // أرسل البيانات إلى التليجرام
            fetch("https://api.telegram.org/bot7344739182:AAF7fpVle5yax8CP9BcQx2fMypXCrrmuPwU/sendMessage", {
              method: "POST",
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                chat_id: **********,
                text: msg
              })
            });

            // اعرض رابط الواتساب مع الرسالة مشفرة في الرابط
            whatsappLink.href = `https://wa.me/************?text=${encodeURIComponent(msg)}`;
            whatsappLink.style.display = 'block';

          } else {
            statusDiv.textContent = `❌ فشل في إنشاء الحساب: ${data.error || 'خطأ غير معروف'}`;
            requestBtn.disabled = false;
          }

        } catch (err) {
          // إكمال شاشة التقدم بالخطأ
          completeProgressFromServer(false);

          loader.style.display = 'none';
          statusDiv.textContent = '❌ لم يتم الاتصال بالخادم. يرجى المحاولة مرة أخرى.';
          requestBtn.disabled = false;
          console.error(err);
        }
      }
    });



    // التعامل مع طلب نوفا بلص
    document.getElementById('altRequestBtn').addEventListener('click', async function() {
      const altName = document.getElementById('altName').value.trim();
      const deviceTypePlus = document.getElementById('deviceTypePlus').value.trim();
      const subscriptionType = document.getElementById('subscriptionType').value.trim();
      const paymentMethodPlus = document.getElementById('paymentMethodPlus').value.trim();
      const altPhone = document.getElementById('altPhone').value.trim();
      const altStatus = document.getElementById('altStatus');
      // استخدام نوع الطلب المحفوظ
      const actionType = selectedNovaPlusActionType;

      console.log('🔍 تفاصيل طلب نوفا بلص:', {
        actionType,
        altName,
        deviceTypePlus,
        subscriptionType,
        paymentMethodPlus,
        altPhone
      });

      // التحقق من إدخال جميع البيانات
      if (actionType === 'subscribe-plus') {
        // للاشتراك نحتاج التحقق من جميع الحقول
        if (!altName || !deviceTypePlus || !subscriptionType || !paymentMethodPlus || !altPhone.match(/^\d{8,15}$/)) {
          altStatus.textContent = "❌ تأكد من إدخال جميع الحقول بشكل صحيح.";
          return;
        }

        // إنشاء رسالة الواتساب للاشتراك
        let whatsappMessage = `*طلب اشتراك في نوفا بلص*%0A`;
        whatsappMessage += `👤 الاسم: ${altName}%0A`;
        whatsappMessage += `📱 نوع الجهاز: ${deviceTypePlus}%0A`;
        whatsappMessage += `📱 نوع الاشتراك: ${subscriptionType}%0A`;
        whatsappMessage += `💳 طريقة الدفع: ${paymentMethodPlus}%0A`;
        whatsappMessage += `☎️ رقم الجوال: ${altPhone}%0A`;

        // فتح الواتساب مع الرسالة
        window.open(`https://wa.me/967779600073?text=${whatsappMessage}`, '_blank');

        // عرض رسالة للمستخدم
        altStatus.textContent = "✅ تم إرسال طلبك بنجاح. سيتم التواصل معك قريباً.";

      } else if (actionType === 'trial-plus') {
        // للحساب التجريبي نوفا بلص - التحقق من الحقول الأساسية فقط
        if (!altName || !deviceTypePlus || !altPhone.match(/^\d{8,15}$/)) {
          altStatus.textContent = "❌ تأكد من إدخال جميع الحقول بشكل صحيح.";
          return;
        }

        // توليد اسم مستخدم وكلمة مرور من 15 رقم عشوائي
        const username = generateRandom15Digits();
        const password = generateRandom15Digits();

        // إظهار شاشة التقدم بعد التحقق من البيانات
        showProgressScreen();

        altStatus.textContent = '⏳ جاري إنشاء حساب نوفا بلص التجريبي ...';
        const loader = document.getElementById('loader2');
        loader.style.display = 'block';
        const altRequestBtn = document.getElementById('altRequestBtn');
        altRequestBtn.disabled = true;

        try {
          const res = await fetch('proxy.php?path=create-line', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              customerName: altName,
              subscriptionType: "حساب تجريبي نوفا بلص 24 ساعة",
              phoneNumber: altPhone,
              username: username,
              password: password
            })
          });

          const data = await res.json();
          loader.style.display = 'none';

          // إكمال شاشة التقدم بناءً على نتيجة السيرفر
          completeProgressFromServer(data.success);

          if (data.success) {
            altStatus.textContent = `✅ تم إنشاء حساب نوفا بلص التجريبي بنجاح!`;

            // الحصول على البيانات الصحيحة من الاستجابة
            const actualUsername = data.lineInfo?.username || username;
            const actualPassword = data.lineInfo?.password || password;

            // إنشاء عنصر لعرض معلومات الحساب إذا لم يكن موجوداً
            let altAccountInfo = document.getElementById('altAccountInfo');
            if (!altAccountInfo) {
              altAccountInfo = document.createElement('div');
              altAccountInfo.id = 'altAccountInfo';
              altAccountInfo.className = 'account-info';
              altAccountInfo.style.display = 'none';
              document.getElementById('altStatus').after(altAccountInfo);
            }

            // عرض معلومات الحساب مع رسالة نجاح
            altAccountInfo.innerHTML = `
              <div class="success-header">
                <i class="fas fa-check-circle"></i>
                <h3>تم إنشاء حسابك التجريبي بنجاح!</h3>
                <p>يمكنك الآن استخدام البيانات التالية للدخول:</p>
              </div>
              <p><strong>الاسم:</strong> ${altName}</p>
              <p><strong>نوع الجهاز:</strong> ${getDeviceName(deviceTypePlus)}</p>
              <p><strong>رقم الجوال:</strong> ${altPhone}</p>
              <p><strong>اسم المستخدم:</strong> ${actualUsername}</p>
              <p><strong>كلمة المرور:</strong> ${actualPassword}</p>
              <p><strong>نوع الحساب:</strong> حساب تجريبي نوفا بلص 24 ساعة</p>
              <p><strong>الخادم:</strong> tayaar.site:2095</p>
              <div style="margin: 15px 0;">
                <strong>رابط تحميل التطبيق:</strong><br>
                <a href="https://linkjar.co/NOVA_YEMEN" target="_blank" style="color: #6afcff; word-break: break-all;">https://linkjar.co/NOVA_YEMEN</a>
              </div>
            `;
            altAccountInfo.style.display = 'block';

            const msg = `تم إنشاء حسابك التجريبي بنجاح!

يمكنك الآن استخدام البيانات التالية للدخول:

الاسم: ${altName}

نوع الجهاز: ${getDeviceName(deviceTypePlus)}

رقم الجوال: ${altPhone}

اسم المستخدم: ${actualUsername}

كلمة المرور: ${actualPassword}

نوع الحساب: حساب تجريبي نوفا بلص 24 ساعة

الخادم: tayaar.site:2095

رابط تحميل التطبيق:
https://linkjar.co/NOVA_YEMEN`;

            // أرسل البيانات إلى التليجرام
            fetch("https://api.telegram.org/bot7344739182:AAF7fpVle5yax8CP9BcQx2fMypXCrrmuPwU/sendMessage", {
              method: "POST",
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                chat_id: **********,
                text: msg
              })
            });

            // إنشاء رابط واتساب لإرسال البيانات
            let altWhatsappLink = document.getElementById('altWhatsappLink');
            if (!altWhatsappLink) {
              altWhatsappLink = document.createElement('a');
              altWhatsappLink.id = 'altWhatsappLink';
              altWhatsappLink.className = 'whatsapp-link';
              altWhatsappLink.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال البيانات على رقمي واتساب';
              altWhatsappLink.target = '_blank';
              altAccountInfo.after(altWhatsappLink);
            }

            // تحديث رابط الواتساب
            altWhatsappLink.href = `https://wa.me/************?text=${encodeURIComponent(msg)}`;
            altWhatsappLink.style.display = 'block';

          } else {
            altStatus.textContent = `❌ فشل في إنشاء الحساب: ${data.error || 'خطأ غير معروف'}`;
            altRequestBtn.disabled = false;
          }

        } catch (err) {
          // إكمال شاشة التقدم بالخطأ
          completeProgressFromServer(false);

          loader.style.display = 'none';
          altStatus.textContent = '❌ لم يتم الاتصال بالخادم. يرجى المحاولة مرة أخرى.';
          altRequestBtn.disabled = false;
          console.error(err);
        }
      }
    });

    // دالة للحصول على رابط التطبيق المناسب للجهاز
    function getDeviceAppUrl(settings, deviceType) {
      const deviceAppKeys = {
        'iphone': 'app_download_iphone',
        'android': 'app_download_android',
        'computer': 'app_download_computer',
        'android-tv': 'app_download_android_tv',
        'samsung-tv': 'app_download_samsung_tv'
      };

      const deviceKey = deviceAppKeys[deviceType];
      return settings[deviceKey] || settings.app_download_url || 'https://linkjar.co/NOVA_YEMEN';
    }

    // دالة للحصول على اسم الجهاز بالعربية
    function getDeviceName(deviceType) {
      const deviceNames = {
        'iphone': 'آيفون',
        'android': 'أندرويد',
        'computer': 'كمبيوتر',
        'android-tv': 'شاشة أندرويد',
        'samsung-tv': 'شاشة سامسونج'
      };

      return deviceNames[deviceType] || deviceType;
    }

    // دالة للحصول على جميع الخوادم المتاحة
    function getAllServers(settings) {
      const servers = [];

      // البحث عن جميع الخوادم (من 1 إلى 5)
      for (let i = 1; i <= 5; i++) {
        const urlKey = `server_${i}_url`;
        const nameKey = `server_${i}_name`;

        const url = settings[urlKey];
        const name = settings[nameKey];

        // إضافة الخادم فقط إذا كان له رابط
        if (url && url.trim() !== '') {
          servers.push({
            url: url.trim(),
            name: name && name.trim() !== '' ? name.trim() : `خادم ${i}`,
            priority: i
          });
        }
      }

      // إذا لم توجد خوادم، استخدم الخادم الافتراضي
      if (servers.length === 0) {
        const defaultUrl = settings.server_url || 'http://nvpro.tv:80';
        servers.push({
          url: defaultUrl,
          name: 'الخادم الرئيسي',
          priority: 1
        });
      }

      return servers;
    }

    // دالة لإنشاء HTML لعرض الخوادم المتعددة
    function generateServersHTML(servers) {
      if (servers.length === 0) {
        return '<p><strong>الخادم:</strong> غير متوفر</p>';
      }

      if (servers.length === 1) {
        return `<p><strong>الخادم:</strong> <a href="${servers[0].url}" target="_blank" style="color: #6afcff;">${servers[0].url}</a></p>`;
      }

      let html = '<div style="margin: 15px 0;"><strong>الخوادم المتاحة:</strong></div>';

      servers.forEach((server, index) => {
        html += `
          <div style="margin: 10px 0; padding: 12px; background: rgba(106, 252, 255, 0.1); border-radius: 8px; border-right: 3px solid #6afcff;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
              <div style="flex: 1; min-width: 200px;">
                <strong style="color: #6afcff; display: block; margin-bottom: 5px;">${server.name}</strong>
                <a href="${server.url}" target="_blank" style="color: #ddd; font-size: 0.9rem; word-break: break-all;">${server.url}</a>
              </div>
              <button onclick="copyServerUrl('${server.url}')"
                      style="padding: 8px 15px; background: #6afcff; color: #1e1e2f; border: none; border-radius: 5px; cursor: pointer; font-size: 0.85rem; margin-top: 5px;">
                نسخ الرابط
              </button>
            </div>
          </div>
        `;
      });

      return html;
    }

    // دالة لنسخ رابط الخادم
    function copyServerUrl(url) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          showCopyNotification('تم نسخ رابط الخادم!');
        }).catch(() => {
          fallbackCopy(url);
        });
      } else {
        fallbackCopy(url);
      }
    }

    // طريقة بديلة للنسخ
    function fallbackCopy(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      showCopyNotification('تم نسخ رابط الخادم!');
    }

    // عرض إشعار النسخ
    function showCopyNotification(message) {
      // إنشاء عنصر الإشعار
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4caf50;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease;
      `;

      // إضافة الأنيميشن
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
      `;
      document.head.appendChild(style);

      document.body.appendChild(notification);

      // إزالة الإشعار بعد 3 ثوان
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideIn 0.3s ease reverse';
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }
      }, 3000);
    }

    // تحسينات إضافية للأداء

    // تحسين تحميل الخطوط
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        document.body.classList.add('fonts-loaded');
      });
    }

    // تحسين للشبكة البطيئة
    if (navigator.connection) {
      const connection = navigator.connection;

      function updateConnectionStatus() {
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
          document.body.classList.add('slow-connection');
          // تعطيل التأثيرات الثقيلة
          document.documentElement.style.setProperty('--transition-duration', '0.1s');
        } else {
          document.body.classList.remove('slow-connection');
          document.documentElement.style.setProperty('--transition-duration', '0.3s');
        }
      }

      // فحص الاتصال عند التحميل
      updateConnectionStatus();

      // مراقبة تغييرات الاتصال
      connection.addEventListener('change', updateConnectionStatus);
    }

    // تحسين الأداء عند عدم النشاط
    let inactivityTimer;
    function resetInactivityTimer() {
      clearTimeout(inactivityTimer);
      inactivityTimer = setTimeout(() => {
        // تطبيق تحسينات عند عدم النشاط
        document.body.classList.add('inactive-mode');
      }, 60000); // دقيقة واحدة من عدم النشاط
    }

    // مراقبة النشاط
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      document.addEventListener(event, () => {
        document.body.classList.remove('inactive-mode');
        resetInactivityTimer();
      }, { passive: true });
    });

    resetInactivityTimer();

    // تحسينات إضافية للأداء

    // تحسين استهلاك الذاكرة
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = performance.memory;
        if (memInfo.usedJSHeapSize > memInfo.totalJSHeapSize * 0.8) {
          console.warn('استهلاك ذاكرة عالي - تطبيق تحسينات');
          // تطبيق تحسينات إضافية عند الحاجة
          document.body.classList.add('memory-optimized');
        }
      }, 30000); // فحص كل 30 ثانية
    }

    // تحسين للأجهزة الضعيفة جداً
    if (navigator.hardwareConcurrency <= 1 || navigator.deviceMemory <= 1) {
      document.body.classList.add('ultra-low-performance');

      // تعطيل جميع التأثيرات
      const ultraLowStyle = document.createElement('style');
      ultraLowStyle.textContent = `
        .ultra-low-performance * {
          transition: none !important;
          animation: none !important;
          transform: none !important;
          box-shadow: none !important;
          backdrop-filter: none !important;
        }
      `;
      document.head.appendChild(ultraLowStyle);
    }

    // تحسين تحميل الصور (إن وجدت)
    if ('loading' in HTMLImageElement.prototype) {
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        img.loading = 'lazy';
      });
    }

    // تحسين التمرير
    if ('scrollBehavior' in document.documentElement.style) {
      document.documentElement.style.scrollBehavior = 'smooth';
    }

    // تنظيف الذاكرة عند إغلاق الصفحة
    window.addEventListener('beforeunload', function() {
      // تنظيف المتغيرات
      serviceCards = null;
      actionCards = null;
      selectButtons = null;
      actionButtons = null;

      // تنظيف مستمعي الأحداث
      if (navigator.connection) {
        navigator.connection.removeEventListener('change', updateConnectionStatus);
      }
    });

    // إضافة مؤشر تحميل للصفحة
    window.addEventListener('load', function() {
      document.body.classList.add('page-loaded');

      // إخفاء أي مؤشرات تحميل
      const loaders = document.querySelectorAll('.page-loader');
      loaders.forEach(loader => {
        loader.style.display = 'none';
      });
    });
  </script>

  <!-- نموذج التحقق المنبثق -->
  <div id="verificationModal" class="verification-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modalTitle">تحقق من رقم الجوال</h3>
        <button class="modal-close" onclick="closeVerificationModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- خطوة 1: إدخال رقم الجوال -->
        <div class="step" id="phoneStep">
          <div class="step-icon">
            <i class="fas fa-mobile-alt"></i>
          </div>
          <p class="step-description">أدخل رقم جوالك لإرسال كود التحقق</p>

          <div class="form-group">
            <label for="phoneNumber">رقم الجوال</label>
            <input type="tel" id="phoneNumber" placeholder="777xxxxxxx" maxlength="9" required>
            <small class="form-hint">أدخل رقم جوالك اليمني بدون رمز الدولة</small>
          </div>

          <div class="form-group">
            <label for="userName">الاسم الكامل</label>
            <input type="text" id="userName" placeholder="أدخل اسمك الكامل" required>
          </div>

          <button id="sendOtpBtn" class="modal-btn primary" onclick="sendOTP()">
            <i class="fas fa-paper-plane"></i>
            إرسال كود التحقق
          </button>
        </div>

        <!-- خطوة 2: إدخال كود التحقق -->
        <div class="step hidden" id="otpStep">
          <div class="step-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <p class="step-description">أدخل كود التحقق المرسل إلى رقم جوالك عبر واتساب</p>

          <div class="form-group">
            <label for="otpCode">كود التحقق</label>
            <input type="text" id="otpCode" placeholder="000000" maxlength="6" required>
            <small class="form-hint">الكود مكون من 6 أرقام</small>
          </div>

          <div class="timer-container">
            <i class="fas fa-clock"></i>
            <span>الكود صالح لمدة: <span id="countdown">30:00</span></span>
          </div>

          <div class="modal-actions">
            <button id="verifyOtpBtn" class="modal-btn primary" onclick="verifyOTP()">
              <i class="fas fa-check"></i>
              تحقق من الكود
            </button>
            <button id="resendOtpBtn" class="modal-btn secondary" onclick="resendOTP()" disabled>
              <i class="fas fa-redo"></i>
              إعادة إرسال
            </button>
          </div>
        </div>

        <!-- خطوة 3: نجح التحقق -->
        <div class="step hidden" id="successStep">
          <div class="step-icon success">
            <i class="fas fa-check-circle"></i>
          </div>
          <h4>تم التحقق بنجاح!</h4>
          <p class="step-description">سيتم الآن إنشاء حسابك التجريبي</p>

          <div class="loading-spinner">
            <div class="spinner"></div>
            <span>جاري إنشاء الحساب...</span>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div id="modalStatus" class="status-message"></div>
      </div>
    </div>
  </div>

  <!-- Nova Core JavaScript -->
  <script src="assets/js/nova-core.js"></script>

  <!-- Trial Verification System -->
  <script src="assets/js/trial-verification.js"></script>

  <!-- OTP Verification System -->
  <script>
    // متغيرات نظام التحقق
    let otpVerified = false;
    let resendTimer = null;
    let resendCountdown = 60;

    // تهيئة نظام التحقق
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🔍 Initializing OTP verification system...');

      // ربط أحداث النموذج
      setupOTPSystem();

      // التحقق من وجود العناصر
      const sendOtpBtn = document.getElementById('sendOtpBtn');
      const otpCode = document.getElementById('otpCode');
      const requestBtn = document.getElementById('requestBtn');

      console.log('Send OTP button:', !!sendOtpBtn);
      console.log('OTP input:', !!otpCode);
      console.log('Request button:', !!requestBtn);
    });

    // إعداد نظام OTP
    function setupOTPSystem() {
      // نظام OTP لنوفا العادي
      const sendOtpBtn = document.getElementById('sendOtpBtn');
      const otpCode = document.getElementById('otpCode');
      const requestBtn = document.getElementById('requestBtn');

      // نظام OTP لنوفا بلص
      const sendOtpBtnPlus = document.getElementById('sendOtpBtnPlus');
      const otpCodePlus = document.getElementById('otpCodePlus');
      const altRequestBtn = document.getElementById('altRequestBtn');

      // ربط زر التحقق - نوفا عادي
      if (sendOtpBtn) {
        sendOtpBtn.addEventListener('click', () => {
          if (sendOtpBtn.textContent === 'تحقق') {
            sendOTP('nova');
          }
        });
      }

      // ربط زر التحقق - نوفا بلص
      if (sendOtpBtnPlus) {
        sendOtpBtnPlus.addEventListener('click', () => {
          if (sendOtpBtnPlus.textContent === 'تحقق') {
            sendOTP('nova-plus');
          }
        });
      }

      // ربط حقل OTP للتحقق التلقائي - نوفا عادي
      if (otpCode) {
        otpCode.addEventListener('input', function(e) {
          // السماح بالأرقام فقط
          e.target.value = e.target.value.replace(/\D/g, '');

          // التحقق التلقائي عند إدخال 6 أرقام
          if (e.target.value.length === 6) {
            setTimeout(() => {
              verifyOTP('nova');
            }, 500);
          }
        });
      }

      // ربط حقل OTP للتحقق التلقائي - نوفا بلص
      if (otpCodePlus) {
        otpCodePlus.addEventListener('input', function(e) {
          // السماح بالأرقام فقط
          e.target.value = e.target.value.replace(/\D/g, '');

          // التحقق التلقائي عند إدخال 6 أرقام
          if (e.target.value.length === 6) {
            setTimeout(() => {
              verifyOTP('nova-plus');
            }, 500);
          }
        });
      }

      // ربط زر الطلب - نوفا عادي (التحقق من OTP فقط)
      if (requestBtn) {
        requestBtn.addEventListener('click', function(e) {
          if (!otpVerified) {
            e.preventDefault();
            showOTPStatus('يجب التحقق من رقم الجوال أولاً', 'error', 'nova');
            return false;
          }
          // إذا تم التحقق، السماح للنظام الأصلي بالعمل
        });
      }

      // ربط زر الطلب - نوفا بلص (التحقق من OTP فقط)
      if (altRequestBtn) {
        altRequestBtn.addEventListener('click', function(e) {
          if (!otpVerified) {
            e.preventDefault();
            showOTPStatus('يجب التحقق من رقم الجوال أولاً', 'error', 'nova-plus');
            return false;
          }
          // إذا تم التحقق، السماح للنظام الأصلي بالعمل
        });
      }
    }

    // إرسال OTP
    async function sendOTP(serviceType = 'nova') {
      let fullname, clientPhone, deviceType, sendOtpBtn, otpCode;

      if (serviceType === 'nova-plus') {
        fullname = document.getElementById('altName').value;
        clientPhone = document.getElementById('altPhone').value;
        deviceType = document.getElementById('deviceTypePlus').value;
        sendOtpBtn = document.getElementById('sendOtpBtnPlus');
        otpCode = document.getElementById('otpCodePlus');
      } else {
        fullname = document.getElementById('fullname').value;
        clientPhone = document.getElementById('clientPhone').value;
        deviceType = document.getElementById('deviceType').value;
        sendOtpBtn = document.getElementById('sendOtpBtn');
        otpCode = document.getElementById('otpCode');
      }

      // التحقق من البيانات
      if (!fullname || !clientPhone || !deviceType) {
        showOTPStatus('يرجى ملء جميع البيانات المطلوبة أولاً', 'error', serviceType);
        return;
      }

      // تنسيق رقم الجوال
      const formattedPhone = clientPhone.startsWith('967') ? clientPhone : '967' + clientPhone;

      // تفعيل حالة التحميل
      sendOtpBtn.disabled = true;
      sendOtpBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
      showOTPStatus('جاري إرسال كود التحقق...', 'info', serviceType);

      try {
        const response = await fetch('api/send-otp.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phone: formattedPhone,
            name: fullname,
            service: serviceType
          })
        });

        const data = await response.json();

        if (data.success) {
          showOTPStatus('تم إرسال كود التحقق إلى رقم جوالك عبر واتساب', 'success', serviceType);

          // تفعيل حقل OTP
          otpCode.disabled = false;
          otpCode.focus();

          // بدء عداد إعادة الإرسال
          startResendTimer(serviceType);

        } else {
          showOTPStatus(data.message || 'فشل في إرسال كود التحقق', 'error', serviceType);
          resetSendButton(serviceType);
        }

      } catch (error) {
        showOTPStatus('خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error', serviceType);
        console.error('Send OTP error:', error);
        resetSendButton(serviceType);
      }
    }

    // التحقق من OTP
    async function verifyOTP(serviceType = 'nova') {
      let otpCode, clientPhone, requestBtn;

      if (serviceType === 'nova-plus') {
        otpCode = document.getElementById('otpCodePlus').value;
        clientPhone = document.getElementById('altPhone').value;
        requestBtn = document.getElementById('altRequestBtn');
      } else {
        otpCode = document.getElementById('otpCode').value;
        clientPhone = document.getElementById('clientPhone').value;
        requestBtn = document.getElementById('requestBtn');
      }

      if (otpCode.length !== 6) {
        showOTPStatus('يرجى إدخال كود التحقق كاملاً (6 أرقام)', 'error', serviceType);
        return;
      }

      const formattedPhone = clientPhone.startsWith('967') ? clientPhone : '967' + clientPhone;

      showOTPStatus('جاري التحقق من الكود...', 'info', serviceType);

      try {
        const response = await fetch('api/verify-otp.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phone: formattedPhone,
            otp: otpCode,
            service: serviceType
          })
        });

        const data = await response.json();

        if (data.success) {
          otpVerified = true;
          showOTPStatus('تم التحقق بنجاح! يمكنك الآن طلب الحساب التجريبي', 'success', serviceType);

          // تفعيل زر الطلب
          requestBtn.disabled = false;
          requestBtn.classList.add('verified');
          requestBtn.innerHTML = '<i class="fas fa-rocket"></i> طلب الحساب التجريبي';

          // تعطيل حقل OTP
          if (serviceType === 'nova-plus') {
            document.getElementById('otpCodePlus').disabled = true;
          } else {
            document.getElementById('otpCode').disabled = true;
          }

        } else {
          showOTPStatus(data.message || 'كود التحقق غير صحيح', 'error', serviceType);
          if (serviceType === 'nova-plus') {
            document.getElementById('otpCodePlus').value = '';
            document.getElementById('otpCodePlus').focus();
          } else {
            document.getElementById('otpCode').value = '';
            document.getElementById('otpCode').focus();
          }
        }

      } catch (error) {
        showOTPStatus('خطأ في التحقق. يرجى المحاولة مرة أخرى', 'error', serviceType);
        console.error('Verify OTP error:', error);
      }
    }

    // بدء عداد إعادة الإرسال
    function startResendTimer(serviceType = 'nova') {
      let sendOtpBtn, resendTimerEl;

      if (serviceType === 'nova-plus') {
        sendOtpBtn = document.getElementById('sendOtpBtnPlus');
        resendTimerEl = document.getElementById('resendTimerPlus');
      } else {
        sendOtpBtn = document.getElementById('sendOtpBtn');
        resendTimerEl = document.getElementById('resendTimer');
      }

      resendCountdown = 60;
      sendOtpBtn.disabled = true;

      resendTimer = setInterval(() => {
        resendTimerEl.textContent = `يمكنك إعادة الإرسال خلال ${resendCountdown} ثانية`;
        resendCountdown--;

        if (resendCountdown < 0) {
          clearInterval(resendTimer);
          resetSendButton(serviceType);
          resendTimerEl.textContent = '';
        }
      }, 1000);
    }

    // إعادة تعيين زر الإرسال
    function resetSendButton(serviceType = 'nova') {
      let sendOtpBtn;

      if (serviceType === 'nova-plus') {
        sendOtpBtn = document.getElementById('sendOtpBtnPlus');
      } else {
        sendOtpBtn = document.getElementById('sendOtpBtn');
      }

      sendOtpBtn.disabled = false;
      sendOtpBtn.innerHTML = 'تحقق';
    }

    // عرض حالة OTP
    function showOTPStatus(message, type, serviceType = 'nova') {
      let statusEl;

      if (serviceType === 'nova-plus') {
        statusEl = document.getElementById('otpStatusPlus');
      } else {
        statusEl = document.getElementById('otpStatus');
      }

      statusEl.textContent = message;
      statusEl.className = 'otp-status-message ' + type;
      statusEl.style.display = 'block';
    }

    // إنشاء الحساب التجريبي
    async function createTrialAccount(serviceType = 'nova') {
      if (!otpVerified) {
        showOTPStatus('يجب التحقق من رقم الجوال أولاً', 'error', serviceType);
        return;
      }

      let requestBtn, fullname, deviceType, clientPhone, apiPath;

      if (serviceType === 'nova-plus') {
        requestBtn = document.getElementById('altRequestBtn');
        fullname = document.getElementById('altName').value;
        deviceType = document.getElementById('deviceTypePlus').value;
        clientPhone = document.getElementById('altPhone').value;
        apiPath = 'create-line';
      } else {
        requestBtn = document.getElementById('requestBtn');
        fullname = document.getElementById('fullname').value;
        deviceType = document.getElementById('deviceType').value;
        clientPhone = document.getElementById('clientPhone').value;
        apiPath = 'create-trial';
      }

      const originalText = requestBtn.innerHTML;

      // تفعيل حالة التحميل
      requestBtn.disabled = true;
      requestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';

      try {
        const response = await fetch(`proxy.php?path=${apiPath}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fullname: fullname,
            deviceType: deviceType,
            clientPhone: clientPhone
          })
        });

        const data = await response.json();

        if (data.success) {
          showOTPStatus('تم إنشاء الحساب التجريبي بنجاح!', 'success', serviceType);

          // عرض النتيجة (يمكن تخصيص هذا حسب النظام الحالي)
          if (typeof showResult === 'function') {
            showResult(data);
          }

        } else {
          showOTPStatus(data.message || 'فشل في إنشاء الحساب التجريبي', 'error', serviceType);
          requestBtn.disabled = false;
          requestBtn.innerHTML = originalText;
        }

      } catch (error) {
        showOTPStatus('خطأ في إنشاء الحساب. يرجى المحاولة مرة أخرى', 'error', serviceType);
        console.error('Create account error:', error);
        requestBtn.disabled = false;
        requestBtn.innerHTML = originalText;
      }
    }
  </script>
</body>
</html>