<?php
// إعدادات قاعدة البيانات لهوستنقر
class Database {
    // ✅ بيانات قاعدة البيانات الصحيحة لهوستنقر
    private $host = 'localhost';
    private $db_name = 'u734249436_nova_settings'; // ✅ اسم قاعدة البيانات الصحيح
    private $username = 'u734249436_nova_user';    // ✅ اسم المستخدم الصحيح
    private $password = 'MTwajdan7$';               // ✅ كلمة المرور الصحيحة
    private $conn;

    // الحصول على اتصال قاعدة البيانات
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            // في بيئة الإنتاج، لا تعرض تفاصيل الخطأ للمستخدمين
            error_log("Database connection error: " . $exception->getMessage());
            
            // عرض رسالة عامة للمستخدم
            if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'خطأ في الاتصال بقاعدة البيانات']);
            } else {
                echo "خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.";
            }
            exit();
        }

        return $this->conn;
    }
    
    // اختبار الاتصال
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return ['success' => true, 'message' => 'تم الاتصال بقاعدة البيانات بنجاح'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
        return ['success' => false, 'error' => 'فشل في الاتصال'];
    }
}

// كلاس لإدارة إعدادات الخدمة
class ServiceSettings {
    private $conn;
    private $table_name = "service_settings";

    public function __construct($db) {
        $this->conn = $db;
    }

    // جلب جميع إعدادات خدمة معينة
    public function getServiceSettings($service_name) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE service_name = ? AND is_active = 1 ORDER BY setting_key";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(1, $service_name);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting service settings: " . $e->getMessage());
            return [];
        }
    }

    // جلب إعداد واحد
    public function getSetting($service_name, $setting_key) {
        try {
            $query = "SELECT setting_value FROM " . $this->table_name . " WHERE service_name = ? AND setting_key = ? AND is_active = 1";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(1, $service_name);
            $stmt->bindParam(2, $setting_key);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row ? $row['setting_value'] : null;
        } catch (PDOException $e) {
            error_log("Error getting setting: " . $e->getMessage());
            return null;
        }
    }

    // تحديث إعداد
    public function updateSetting($service_name, $setting_key, $setting_value) {
        try {
            $query = "UPDATE " . $this->table_name . " SET setting_value = ?, updated_at = NOW() WHERE service_name = ? AND setting_key = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(1, $setting_value);
            $stmt->bindParam(2, $service_name);
            $stmt->bindParam(3, $setting_key);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating setting: " . $e->getMessage());
            return false;
        }
    }

    // إضافة إعداد جديد
    public function addSetting($service_name, $setting_key, $setting_value, $setting_label) {
        try {
            $query = "INSERT INTO " . $this->table_name . " (service_name, setting_key, setting_value, setting_label) VALUES (?, ?, ?, ?)";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(1, $service_name);
            $stmt->bindParam(2, $setting_key);
            $stmt->bindParam(3, $setting_value);
            $stmt->bindParam(4, $setting_label);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error adding setting: " . $e->getMessage());
            return false;
        }
    }

    // حذف إعداد (تعطيل)
    public function deleteSetting($id) {
        try {
            $query = "UPDATE " . $this->table_name . " SET is_active = 0 WHERE id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(1, $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error deleting setting: " . $e->getMessage());
            return false;
        }
    }

    // جلب جميع الخدمات المتاحة
    public function getAllServices() {
        try {
            $query = "SELECT DISTINCT service_name FROM " . $this->table_name . " WHERE is_active = 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Error getting all services: " . $e->getMessage());
            return [];
        }
    }
}

// دالة مساعدة لاختبار قاعدة البيانات
function testDatabaseConnection() {
    $database = new Database();
    return $database->testConnection();
}
?>
