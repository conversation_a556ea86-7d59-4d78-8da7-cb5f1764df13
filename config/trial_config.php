<?php
/**
 * إعدادات نظام الحماية للحسابات التجريبية
 * Nova Yemen Trial Protection System Configuration
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'u734249436_wajdan7001'); // غير هذا إلى اسم قاعدة البيانات الخاصة بك
define('DB_USER', 'u734249436_wajdan1');      // غير هذا إلى اسم المستخدم
define('DB_PASS', 'MTwajdan7$');      // غير هذا إلى كلمة المرور

// إعدادات الحماية
define('TRIAL_COOLDOWN_DAYS', 7);        // منع إنشاء حساب جديد لمدة 7 أيام
define('OTP_EXPIRY_MINUTES', 30);        // انتهاء صلاحية كود OTP بعد 30 دقيقة
define('OTP_RESEND_MINUTES', 10);        // منع إعادة إرسال OTP لمدة 10 دقائق
define('MAX_OTP_ATTEMPTS', 3);           // حد أقصى 3 محاولات خاطئة
define('BLOCK_DURATION_HOURS', 24);      // حظر لمدة 24 ساعة بعد تجاوز المحاولات

// إعدادات الخدمات
define('NOVA_TRIAL_HOURS', 12);          // مدة تجربة نوفا العادي
define('NOVA_PLUS_TRIAL_HOURS', 24);     // مدة تجربة نوفا بلص

// إعدادات واتساب
define('WHATSAPP_API_URL', 'http://**************:3000'); // رابط VPS الخاص بواتساب
define('WHATSAPP_NUMBER', '967779600073'); // رقم واتساب الدعم

// إعدادات الأمان
define('MAX_REQUESTS_PER_HOUR', 5);      // حد أقصى 5 طلبات في الساعة من نفس IP
define('ENABLE_IP_BLOCKING', true);      // تفعيل حظر IP المشبوهة
define('LOG_ALL_REQUESTS', true);        // تسجيل جميع الطلبات

// رسائل النظام
define('MESSAGES', [
    'otp_sent' => 'تم إرسال كود التحقق إلى رقم جوالك عبر واتساب',
    'otp_invalid' => 'كود التحقق غير صحيح أو منتهي الصلاحية',
    'otp_expired' => 'انتهت صلاحية كود التحقق، يرجى طلب كود جديد',
    'phone_invalid' => 'رقم الجوال غير صحيح، يرجى إدخال رقم يمني صحيح',
    'trial_exists' => 'لقد أنشأت حساباً تجريبياً خلال آخر 7 أيام، يرجى الانتظار',
    'too_many_attempts' => 'تم تجاوز عدد المحاولات المسموحة، يرجى المحاولة بعد 24 ساعة',
    'otp_resend_wait' => 'يرجى الانتظار 10 دقائق قبل طلب كود جديد',
    'account_created' => 'تم إنشاء حسابك التجريبي بنجاح وإرساله عبر واتساب',
    'system_error' => 'حدث خطأ في النظام، يرجى المحاولة لاحقاً'
]);

// دالة الاتصال بقاعدة البيانات
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return false;
    }
}

// دالة التحقق من صحة رقم الجوال اليمني
function validateYemeniPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // إزالة رمز الدولة إذا كان موجوداً
    if (substr($phone, 0, 3) === '967') {
        $phone = substr($phone, 3);
    }
    
    // التحقق من الطول والبداية
    if (strlen($phone) === 9) {
        $validPrefixes = ['77', '78', '73', '70', '71', '72', '79'];
        $prefix = substr($phone, 0, 2);
        return in_array($prefix, $validPrefixes);
    }
    
    return false;
}

// دالة تنظيف رقم الجوال
function cleanPhoneNumber($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (substr($phone, 0, 3) === '967') {
        $phone = substr($phone, 3);
    }
    
    return $phone;
}

// دالة توليد كود OTP
function generateOTP($length = 6) {
    return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

// دالة تسجيل الأحداث
function logActivity($action, $phone, $data = [], $success = true) {
    if (!LOG_ALL_REQUESTS) return;
    
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => $action,
        'phone' => $phone,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'success' => $success,
        'data' => $data
    ];
    
    $logFile = __DIR__ . '/../logs/trial_system_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
}

// دالة إرسال استجابة JSON
function sendResponse($success, $message, $data = []) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة التحقق من Rate Limiting
function checkRateLimit($ip, $action = 'general') {
    $pdo = getDBConnection();
    if (!$pdo) return true; // السماح في حالة خطأ قاعدة البيانات
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM otp_requests_log 
        WHERE ip_address = ? 
        AND action = 'send' 
        AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    
    $stmt->execute([$ip]);
    $result = $stmt->fetch();
    
    return $result['count'] < MAX_REQUESTS_PER_HOUR;
}

// دالة التحقق من حالة الحظر
function isBlocked($phone) {
    $pdo = getDBConnection();
    if (!$pdo) return false;
    
    $stmt = $pdo->prepare("
        SELECT blocked_until 
        FROM trial_users 
        WHERE phone = ? 
        AND blocked_until > NOW()
    ");
    
    $stmt->execute([$phone]);
    $result = $stmt->fetch();
    
    return $result !== false;
}

// إعدادات إضافية للتطوير
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}
?>
