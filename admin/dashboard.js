// متغيرات عامة
let currentService = 'nova';
let isEditMode = false;
let editingSettingId = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSettings(currentService);
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تبديل التبويبات
    document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', function() {
            const service = this.dataset.service;
            switchService(service);
        });
    });

    // نموذج الإعدادات
    document.getElementById('settingForm').addEventListener('submit', handleFormSubmit);
}

// تبديل الخدمة
function switchService(service) {
    currentService = service;
    
    // تحديث التبويبات
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-service="${service}"]`).classList.add('active');
    
    // تحميل إعدادات الخدمة الجديدة
    loadSettings(service);
}

// تحميل الإعدادات
async function loadSettings(service) {
    showLoading(true);
    
    try {
        const response = await fetch(`../api/settings.php?service=${service}`);
        const data = await response.json();
        
        if (data.success) {
            displaySettings(data.data);
        } else {
            showError('فشل في تحميل الإعدادات');
        }
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        showError('خطأ في الاتصال بالخادم');
    } finally {
        showLoading(false);
    }
}

// عرض الإعدادات
function displaySettings(settings) {
    const grid = document.getElementById('settingsGrid');
    
    if (settings.length === 0) {
        grid.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #888;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 15px;"></i>
                <p>لا توجد إعدادات لهذه الخدمة</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = settings.map(setting => `
        <div class="setting-item">
            <div class="setting-info">
                <div class="setting-label">${setting.setting_label}</div>
                <div class="setting-value">${setting.setting_value}</div>
                <small style="color: #888; margin-top: 5px; display: block;">
                    المفتاح: ${setting.setting_key}
                </small>
            </div>
            <div class="setting-actions">
                <button class="btn btn-edit" onclick="editSetting(${setting.id}, '${setting.setting_key}', '${setting.setting_value}', '${setting.setting_label}', '${setting.service_name}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-delete" onclick="deleteSetting(${setting.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

// فتح نافذة الإضافة
function openAddModal() {
    isEditMode = false;
    editingSettingId = null;
    
    document.getElementById('modalTitle').textContent = 'إضافة إعداد جديد';
    document.getElementById('serviceSelect').value = currentService;
    document.getElementById('settingKey').value = '';
    document.getElementById('settingLabel').value = '';
    document.getElementById('settingValue').value = '';
    
    document.getElementById('settingModal').style.display = 'block';
}

// تعديل إعداد
function editSetting(id, key, value, label, service) {
    isEditMode = true;
    editingSettingId = id;
    
    document.getElementById('modalTitle').textContent = 'تعديل الإعداد';
    document.getElementById('serviceSelect').value = service;
    document.getElementById('settingKey').value = key;
    document.getElementById('settingLabel').value = label;
    document.getElementById('settingValue').value = value;
    
    // تعطيل تعديل المفتاح والخدمة في وضع التعديل
    document.getElementById('settingKey').readOnly = true;
    document.getElementById('serviceSelect').disabled = true;
    
    document.getElementById('settingModal').style.display = 'block';
}

// إغلاق النافذة
function closeModal() {
    document.getElementById('settingModal').style.display = 'none';
    
    // إعادة تفعيل الحقول
    document.getElementById('settingKey').readOnly = false;
    document.getElementById('serviceSelect').disabled = false;
}

// معالجة إرسال النموذج
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = {
        service_name: document.getElementById('serviceSelect').value,
        setting_key: document.getElementById('settingKey').value,
        setting_label: document.getElementById('settingLabel').value,
        setting_value: document.getElementById('settingValue').value
    };
    
    try {
        let response;
        
        if (isEditMode) {
            // تحديث إعداد موجود
            response = await fetch('../api/settings.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // إضافة إعداد جديد
            response = await fetch('../api/settings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess(data.message);
            closeModal();
            loadSettings(currentService);
        } else {
            showError(data.error || 'فشل في حفظ الإعداد');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإعداد:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// حذف إعداد
async function deleteSetting(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الإعداد؟')) {
        return;
    }
    
    try {
        const response = await fetch('../api/settings.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: id })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess(data.message);
            loadSettings(currentService);
        } else {
            showError(data.error || 'فشل في حذف الإعداد');
        }
    } catch (error) {
        console.error('خطأ في حذف الإعداد:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alert = document.getElementById('successAlert');
    alert.textContent = message;
    alert.style.display = 'block';
    
    setTimeout(() => {
        alert.style.display = 'none';
    }, 5000);
}

// عرض رسالة خطأ
function showError(message) {
    const alert = document.getElementById('errorAlert');
    alert.textContent = message;
    alert.style.display = 'block';
    
    setTimeout(() => {
        alert.style.display = 'none';
    }, 5000);
}

// عرض/إخفاء التحميل
function showLoading(show) {
    const loading = document.getElementById('loading');
    const grid = document.getElementById('settingsGrid');
    
    if (show) {
        loading.style.display = 'block';
        grid.style.display = 'none';
    } else {
        loading.style.display = 'none';
        grid.style.display = 'block';
    }
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('settingModal');
    if (event.target === modal) {
        closeModal();
    }
}
