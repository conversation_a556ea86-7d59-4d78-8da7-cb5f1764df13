<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم نوفا يمن - إدارة الإعدادات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Nova Theme CSS -->
    <link rel="stylesheet" href="../assets/css/nova-theme.css">
    <link rel="stylesheet" href="../assets/css/nova-components.css">

    <style>
        :root {
            /* نظام ألوان مريح للعين */
            --primary-color: #4a90e2;
            --primary-dark: #357abd;
            --secondary-color: #7b68ee;
            --accent-color: #50c878;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --error-color: #dc3545;

            /* ألوان الخلفية الهادئة */
            --bg-primary: #1a1d23;
            --bg-secondary: #242831;
            --bg-tertiary: #2d3748;
            --bg-card: rgba(36, 40, 49, 0.95);
            --bg-glass: rgba(45, 55, 72, 0.9);

            /* ألوان النص المريحة */
            --text-primary: #f7fafc;
            --text-secondary: #cbd5e0;
            --text-muted: #a0aec0;

            /* الظلال والحدود الناعمة */
            --shadow-light: 0 4px 20px rgba(74, 144, 226, 0.08);
            --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.25);
            --shadow-heavy: 0 12px 40px rgba(0, 0, 0, 0.35);
            --border-glass: 1px solid rgba(74, 144, 226, 0.15);

            /* المسافات */
            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-2xl: 48px;

            /* نصف الأقطار */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: "Cairo", "Cairo-Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            font-display: swap;

            /* تحسينات الأداء */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeSpeed;
            contain: layout style;
        }
        
        .header {
            background: linear-gradient(135deg,
                rgba(26, 29, 35, 0.95),
                rgba(36, 40, 49, 0.9)
            );
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-medium);
            margin-bottom: var(--spacing-xl);
            border-bottom: 2px solid rgba(74, 144, 226, 0.2);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 0%, rgba(74, 144, 226, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .header h1 {
            color: var(--primary-color);
            text-align: center;
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            text-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
            background: linear-gradient(135deg, var(--primary-color), #6afcff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            margin-bottom: var(--spacing-sm);
        }

        .header p {
            text-align: center;
            color: var(--text-secondary);
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
        }
        
        .service-tabs {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            justify-content: center;
            flex-wrap: wrap;
        }

        .tab-button {
            background: var(--bg-card);
            color: var(--text-secondary);
            border: 2px solid rgba(74, 144, 226, 0.2);
            padding: var(--spacing-md) var(--spacing-xl);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 1.1rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            min-width: 150px;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(74, 144, 226, 0.1),
                transparent
            );
            transition: left 0.6s ease;
        }

        .tab-button:hover::before {
            left: 100%;
        }

        .tab-button.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-primary);
            transform: translateY(-2px);
        }

        .tab-button:hover:not(.active) {
            background: rgba(74, 144, 226, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .settings-panel {
            background: var(--bg-card);
            padding: var(--spacing-2xl);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-medium);
            border: 2px solid rgba(74, 144, 226, 0.2);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            margin-bottom: var(--spacing-xl);
        }

        .settings-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 70% 30%, rgba(74, 144, 226, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .settings-grid {
            display: grid;
            gap: var(--spacing-lg);
            position: relative;
            z-index: 2;
        }

        .setting-item {
            background: rgba(255, 255, 255, 0.05);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            border: 2px solid rgba(74, 144, 226, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .setting-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(74, 144, 226, 0.1),
                transparent
            );
            transition: left 0.6s ease;
        }

        .setting-item:hover::before {
            left: 100%;
        }

        .setting-item:hover {
            border-color: rgba(74, 144, 226, 0.4);
            box-shadow: var(--shadow-light);
            transform: translateY(-2px);
            background: rgba(74, 144, 226, 0.1);
        }

        .setting-info {
            flex: 1;
            position: relative;
            z-index: 2;
        }

        .setting-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .setting-item:hover .setting-label {
            color: #6afcff;
        }
        
        .setting-value {
            color: var(--text-secondary);
            word-break: break-all;
            font-size: 0.95rem;
            position: relative;
            z-index: 2;
        }

        .setting-actions {
            display: flex;
            gap: var(--spacing-sm);
            position: relative;
            z-index: 2;
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-edit {
            background: linear-gradient(135deg, var(--warning-color), #e0a800);
            color: white;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #e0a800, var(--warning-color));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .btn-delete {
            background: linear-gradient(135deg, var(--error-color), #c82333);
            color: white;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #c82333, var(--error-color));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-add {
            background: linear-gradient(135deg, var(--success-color), #218838);
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            border: 1px solid rgba(40, 167, 69, 0.3);
            font-size: 1rem;
        }

        .btn-add:hover {
            background: linear-gradient(135deg, #218838, var(--success-color));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn-add:hover {
            background: linear-gradient(135deg, #388e3c, #4caf50);
            transform: translateY(-2px);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .modal-content {
            background: var(--bg-card);
            margin: 5% auto;
            padding: var(--spacing-2xl);
            border-radius: var(--radius-xl);
            width: 90%;
            max-width: 600px;
            border: 2px solid rgba(74, 144, 226, 0.3);
            box-shadow: var(--shadow-heavy);
            position: relative;
            overflow: hidden;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 0%, rgba(74, 144, 226, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .modal h3 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-xl);
            text-align: center;
            font-size: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
            position: relative;
            z-index: 2;
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1rem;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            border: 2px solid rgba(74, 144, 226, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus, .form-group select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
            background: rgba(74, 144, 226, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .btn-save {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: var(--spacing-md) var(--spacing-xl);
            border: 1px solid rgba(74, 144, 226, 0.3);
        }

        .btn-save:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .btn-cancel {
            background: rgba(108, 117, 125, 0.8);
            color: white;
            padding: var(--spacing-md) var(--spacing-xl);
            border: 1px solid rgba(108, 117, 125, 0.3);
        }

        .btn-cancel:hover {
            background: rgba(108, 117, 125, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .loading {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--primary-color);
            position: relative;
            z-index: 2;
        }

        .spinner {
            border: 3px solid rgba(74, 144, 226, 0.1);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--spacing-md);
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            border-radius: var(--radius-md);
            display: none;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            border: 2px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background: rgba(220, 53, 69, 0.1);
            border: 2px solid var(--error-color);
            color: var(--error-color);
        }

        /* تحسينات الأداء */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .settings-panel::before,
            .setting-item::before,
            .modal-content::before {
                display: none !important;
            }
        }
        
        /* Responsive Design المحسن */
        @media (max-width: 768px) {
            .header {
                padding: var(--spacing-lg);
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .container {
                padding: 0 var(--spacing-md);
            }

            .service-tabs {
                flex-direction: column;
                align-items: center;
                gap: var(--spacing-sm);
            }

            .tab-button {
                width: 100%;
                max-width: 300px;
            }

            .setting-item {
                flex-direction: column;
                gap: var(--spacing-md);
                text-align: center;
                padding: var(--spacing-lg);
            }

            .setting-actions {
                justify-content: center;
                flex-wrap: wrap;
            }

            .modal-content {
                margin: 10% auto;
                padding: var(--spacing-lg);
                width: 95%;
            }

            .modal-actions {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .btn-save,
            .btn-cancel {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: var(--spacing-md);
            }

            .settings-panel {
                padding: var(--spacing-lg);
            }

            .setting-item {
                padding: var(--spacing-md);
            }

            .btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div style="max-width: 1400px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 var(--spacing-lg); position: relative; z-index: 2;">
            <div>
                <h1><i class="fas fa-cogs"></i> لوحة تحكم نوفا يمن</h1>
                <p>إدارة وتحكم في جميع خدمات النظام</p>
            </div>
            <a href="logout.php" style="color: white; text-decoration: none; padding: var(--spacing-sm) var(--spacing-lg); border-radius: var(--radius-md); background: linear-gradient(135deg, var(--error-color), #c82333); border: 1px solid rgba(220, 53, 69, 0.3); transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>

    <div class="container">
        <div class="alert alert-success" id="successAlert"></div>
        <div class="alert alert-error" id="errorAlert"></div>
        
        <div class="nova-flex nova-justify-center nova-gap-md nova-flex-wrap nova-mb-xl">
            <button class="nova-btn nova-btn-primary tab-button active" data-service="nova">
                <i class="fas fa-tv"></i> نوفا العادية
            </button>
            <button class="nova-btn nova-btn-secondary tab-button" data-service="nova-plus">
                <i class="fas fa-star"></i> نوفا بلص
            </button>
        </div>

        <div class="settings-panel">
            <button class="nova-btn nova-btn-success nova-btn-lg nova-mb-lg" onclick="openAddModal()">
                <i class="fas fa-plus"></i> إضافة إعداد جديد
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                جاري تحميل الإعدادات...
            </div>
            
            <div class="settings-grid" id="settingsGrid">
                <!-- سيتم تحميل الإعدادات هنا -->
            </div>
        </div>
    </div>

    <!-- نافذة التعديل/الإضافة -->
    <div id="settingModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle">تعديل الإعداد</h3>
            <form id="settingForm">
                <div class="form-group">
                    <label for="serviceSelect">الخدمة:</label>
                    <select id="serviceSelect" required>
                        <option value="nova">نوفا العادية</option>
                        <option value="nova-plus">نوفا بلص</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="settingKey">مفتاح الإعداد:</label>
                    <input type="text" id="settingKey" required placeholder="مثل: server_url">
                </div>
                
                <div class="form-group">
                    <label for="settingLabel">تسمية الإعداد:</label>
                    <input type="text" id="settingLabel" required placeholder="مثل: رابط الخادم">
                </div>
                
                <div class="form-group">
                    <label for="settingValue">قيمة الإعداد:</label>
                    <input type="text" id="settingValue" required placeholder="مثل: http://nvpro.tv:80">
                </div>
                
                <div class="nova-flex nova-justify-center nova-gap-md nova-mt-xl">
                    <button type="submit" class="nova-btn nova-btn-primary nova-btn-lg">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="nova-btn nova-btn-secondary nova-btn-lg" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="dashboard.js"></script>

    <!-- Nova Core JavaScript -->
    <script src="../assets/js/nova-core.js"></script>
</body>
</html>
