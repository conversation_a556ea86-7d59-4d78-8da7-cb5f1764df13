<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة تحكم نوفا يمن</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: "Tajawal", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e2f, #181827);
            color: #eee;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(41, 42, 69, 0.9);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(106, 252, 255, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #6afcff;
            font-size: 2rem;
            text-shadow: 0 0 10px rgba(106, 252, 255, 0.3);
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #aaa;
            font-size: 1rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #ddd;
            font-weight: 600;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #444;
            background: rgba(30, 30, 47, 0.7);
            color: #eee;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            border-color: #6afcff;
            outline: none;
            box-shadow: 0 0 8px rgba(106, 252, 255, 0.3);
            background: rgba(37, 38, 58, 0.9);
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #6afcff, #3bd5e6);
            color: #1e1e2f;
            border: none;
            padding: 16px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(106, 252, 255, 0.3);
            margin-bottom: 20px;
        }
        
        .login-btn:hover {
            background: linear-gradient(135deg, #3bd5e6, #6afcff);
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(106, 252, 255, 0.4);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            text-align: center;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #6afcff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #6afcff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .back-link a:hover {
            color: #3bd5e6;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1><i class="fas fa-shield-alt"></i> نوفا يمن</h1>
            <p>لوحة التحكم الإدارية</p>
        </div>
        
        <div class="alert alert-error" id="errorAlert"></div>
        <div class="alert alert-success" id="successAlert"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i> اسم المستخدم
                </label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> كلمة المرور
                </label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                جاري تسجيل الدخول...
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </button>
        </form>
        
        <div class="back-link">
            <a href="../services (3).html">
                <i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية
            </a>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            
            // إخفاء الرسائل السابقة
            hideAlerts();
            
            // عرض التحميل
            loading.style.display = 'block';
            loginBtn.disabled = true;
            
            try {
                const response = await fetch('login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    showError(data.error || 'خطأ في تسجيل الدخول');
                }
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showError('خطأ في الاتصال بالخادم');
            } finally {
                loading.style.display = 'none';
                loginBtn.disabled = false;
            }
        });
        
        function showError(message) {
            const alert = document.getElementById('errorAlert');
            alert.textContent = message;
            alert.style.display = 'block';
        }
        
        function showSuccess(message) {
            const alert = document.getElementById('successAlert');
            alert.textContent = message;
            alert.style.display = 'block';
        }
        
        function hideAlerts() {
            document.getElementById('errorAlert').style.display = 'none';
            document.getElementById('successAlert').style.display = 'none';
        }
    </script>
</body>
</html>
