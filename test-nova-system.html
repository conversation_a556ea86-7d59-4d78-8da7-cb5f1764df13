<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار النظام المحسن - Nova Yemen</title>
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  
  <!-- Nova Theme CSS -->
  <link rel="stylesheet" href="assets/css/nova-theme.css">
  <link rel="stylesheet" href="assets/css/nova-components.css">
  
  <style>
    .test-section {
      margin: var(--spacing-2xl) 0;
      padding: var(--spacing-xl);
      background: var(--bg-card);
      border-radius: var(--radius-xl);
      border: var(--border-secondary);
    }
    
    .test-title {
      color: var(--primary-color);
      font-size: var(--font-size-2xl);
      margin-bottom: var(--spacing-lg);
      text-align: center;
    }
    
    .test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-top: var(--spacing-lg);
    }
    
    .test-item {
      padding: var(--spacing-lg);
      background: var(--bg-secondary);
      border-radius: var(--radius-lg);
      border: var(--border-secondary);
      text-align: center;
    }
    
    .theme-demo {
      padding: var(--spacing-lg);
      margin: var(--spacing-md) 0;
      border-radius: var(--radius-md);
      border: var(--border-secondary);
    }
    
    .light-demo {
      background: var(--light-bg-card);
      color: var(--light-text-primary);
    }
    
    .dark-demo {
      background: var(--bg-card);
      color: var(--text-primary);
    }
  </style>
</head>
<body>
  <!-- Header المحسن -->
  <header class="nova-header">
    <div class="nova-header-content">
      <a href="index.html" class="nova-logo">Nova Yemen</a>
      
      <nav class="nova-nav">
        <a href="index.html" class="nova-nav-link">الرئيسية</a>
        <a href="services.html" class="nova-nav-link">الخدمات</a>
        <a href="about.html" class="nova-nav-link">من نحن</a>
        <a href="contact.html" class="nova-nav-link">تواصل معنا</a>
        <a href="#" class="nova-nav-link active">اختبار النظام</a>
      </nav>

      <div class="nova-header-controls">
        <!-- زر تبديل الوضع الليلي/النهاري -->
        <button class="nova-theme-toggle" title="تبديل الوضع الليلي/النهاري">
          <i class="fas fa-sun nova-theme-toggle-icon sun"></i>
          <i class="fas fa-moon nova-theme-toggle-icon moon"></i>
        </button>

        <!-- زر قائمة الهامبرغر -->
        <button class="nova-mobile-menu-btn">
          <div class="nova-hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>
  </header>

  <!-- القائمة المحمولة -->
  <div class="nova-mobile-overlay"></div>
  <div class="nova-mobile-menu">
    <div class="nova-mobile-menu-header">
      <div class="nova-logo">Nova Yemen</div>
      <button class="nova-mobile-menu-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <nav class="nova-mobile-menu-nav">
      <a href="index.html" class="nova-nav-link">الرئيسية</a>
      <a href="services.html" class="nova-nav-link">الخدمات</a>
      <a href="about.html" class="nova-nav-link">من نحن</a>
      <a href="contact.html" class="nova-nav-link">تواصل معنا</a>
      <a href="#" class="nova-nav-link active">اختبار النظام</a>
    </nav>
  </div>

  <div class="nova-container">
    <!-- اختبار الوضع الليلي/النهاري -->
    <div class="test-section">
      <h2 class="test-title">🌙 اختبار الوضع الليلي/النهاري</h2>
      <p class="nova-text-center nova-text-secondary nova-mb-lg">
        اضغط على زر التبديل في الأعلى لتجربة الوضع الليلي والنهاري
      </p>
      
      <div class="nova-grid nova-grid-2 nova-gap-lg">
        <div class="theme-demo dark-demo">
          <h3>الوضع الليلي 🌙</h3>
          <p>خلفية داكنة مريحة للعين في الإضاءة المنخفضة</p>
        </div>
        <div class="theme-demo light-demo">
          <h3 style="color: var(--light-text-primary);">الوضع النهاري ☀️</h3>
          <p style="color: var(--light-text-secondary);">خلفية فاتحة مناسبة للإضاءة العالية</p>
        </div>
      </div>
    </div>

    <!-- اختبار الأزرار -->
    <div class="test-section">
      <h2 class="test-title">🔘 اختبار الأزرار</h2>
      <div class="test-grid">
        <button class="nova-btn nova-btn-primary nova-btn-lg">
          <i class="fas fa-star"></i> أساسي
        </button>
        <button class="nova-btn nova-btn-secondary nova-btn-lg">
          <i class="fas fa-heart"></i> ثانوي
        </button>
        <button class="nova-btn nova-btn-success nova-btn-lg">
          <i class="fas fa-check"></i> نجاح
        </button>
        <button class="nova-btn nova-btn-warning nova-btn-lg">
          <i class="fas fa-exclamation"></i> تحذير
        </button>
        <button class="nova-btn nova-btn-danger nova-btn-lg">
          <i class="fas fa-times"></i> خطر
        </button>
        <button class="nova-btn nova-btn-outline-primary nova-btn-lg">
          <i class="fas fa-outline"></i> محدد
        </button>
      </div>
    </div>

    <!-- اختبار البطاقات -->
    <div class="test-section">
      <h2 class="test-title">🃏 اختبار البطاقات</h2>
      <div class="nova-grid nova-grid-auto-md nova-gap-xl">
        <div class="nova-feature-card">
          <i class="fas fa-rocket nova-feature-icon"></i>
          <h3 class="nova-feature-title">سرعة عالية</h3>
          <p class="nova-feature-description">أداء متميز وسرعة في التحميل</p>
        </div>
        <div class="nova-feature-card">
          <i class="fas fa-shield-alt nova-feature-icon"></i>
          <h3 class="nova-feature-title">أمان متقدم</h3>
          <p class="nova-feature-description">حماية قوية لبياناتك</p>
        </div>
        <div class="nova-feature-card">
          <i class="fas fa-mobile-alt nova-feature-icon"></i>
          <h3 class="nova-feature-title">متجاوب</h3>
          <p class="nova-feature-description">يعمل على جميع الأجهزة</p>
        </div>
      </div>
    </div>

    <!-- اختبار نموذج التواصل -->
    <div class="test-section">
      <h2 class="test-title">📞 اختبار نموذج التواصل</h2>
      <div class="nova-text-center">
        <button onclick="window.openContactModal()" class="nova-btn nova-btn-success nova-btn-xl">
          <i class="fas fa-phone"></i> فتح نموذج التواصل
        </button>
      </div>
    </div>

    <!-- اختبار شريط التقدم -->
    <div class="test-section">
      <h2 class="test-title">📊 اختبار شريط التقدم</h2>
      <div class="nova-progress">
        <div class="nova-progress-bar" style="width: 75%"></div>
      </div>
      <p class="nova-text-center nova-mt-md nova-text-secondary">75% مكتمل</p>
    </div>

    <!-- معلومات النظام -->
    <div class="test-section">
      <h2 class="test-title">ℹ️ معلومات النظام</h2>
      <div class="test-item">
        <p><strong>الوضع الحالي:</strong> <span id="current-theme">يتم التحديد...</span></p>
        <p><strong>حجم الشاشة:</strong> <span id="screen-size">يتم التحديد...</span></p>
        <p><strong>نوع الجهاز:</strong> <span id="device-type">يتم التحديد...</span></p>
        <p><strong>حالة الاتصال:</strong> <span id="connection-status">يتم التحديد...</span></p>
      </div>
    </div>
  </div>

  <script>
    // تحديث معلومات النظام
    function updateSystemInfo() {
      // الوضع الحالي
      const currentTheme = document.documentElement.getAttribute('data-theme') || 'تلقائي';
      document.getElementById('current-theme').textContent = currentTheme === 'light' ? 'نهاري ☀️' : 'ليلي 🌙';
      
      // حجم الشاشة
      document.getElementById('screen-size').textContent = `${window.innerWidth} × ${window.innerHeight}`;
      
      // نوع الجهاز
      const deviceType = window.innerWidth <= 768 ? 'جوال 📱' : 
                        window.innerWidth <= 1024 ? 'تابلت 📱' : 'كمبيوتر 💻';
      document.getElementById('device-type').textContent = deviceType;
      
      // حالة الاتصال
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      const connectionStatus = connection ? 
        `${connection.effectiveType || 'غير معروف'} - ${connection.downlink || 'غير معروف'} Mbps` : 
        'غير متاح';
      document.getElementById('connection-status').textContent = connectionStatus;
    }

    // تحديث المعلومات عند التحميل وتغيير الحجم
    updateSystemInfo();
    window.addEventListener('resize', updateSystemInfo);
    
    // مراقبة تغيير الوضع
    const observer = new MutationObserver(updateSystemInfo);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['data-theme'] });

    console.log('🧪 تم تحميل صفحة اختبار النظام بنجاح');
  </script>

  <!-- Nova Core JavaScript -->
  <script src="assets/js/nova-core.js"></script>
</body>
</html>
