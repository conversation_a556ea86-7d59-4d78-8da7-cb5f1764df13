{"translation-revision-date": "2024-12-17 15:38:43+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Reset price filter": ["إعادة تعيين عامل تصفية السعر"], "Reset filter": ["إعادة تعيين التصفية"], "Apply price filter": ["تطبيق عامل تصفية السعر"], "Apply filter": ["تطبيق عامل التصفية"], "Filter products by maximum price": ["تصفية المنتجات حسب الحد الأقصى للسعر"], "Filter products by minimum price": ["تصفية المنتجات حسب الحد الأدنى للسعر"], "There was an error loading the content.": ["حدث خطأ في أثناء تحميل المحتوى."], "Oops!": ["عفوًا!"], "Error:": ["خطأ:"], "Reset": ["إستعادة"], "Apply": ["تطبيق"]}}, "comment": {"reference": "assets/client/blocks/price-filter-frontend.js"}}