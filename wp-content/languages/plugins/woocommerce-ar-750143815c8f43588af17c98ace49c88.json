{"translation-revision-date": "2024-12-17 15:38:43+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["قم بالتبديل لتعطيل فتح أداة السحب من عربة التسوق الصغيرة عند النقر على أيقونة عربة التسوق، وانتقل إلى صفحة السداد بدلاً من ذلك."], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["انتقل إلى السداد عند النقر على عربة التسوق الصغيرة، بدلاً من فتح أداة السحب."], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["لا يعرض المحرر قيمة العدد الحقيقية، لكنه يعرض عنصرًا نائبًا للإشارة إلى الكيفية التي سيبدو بها على الواجهة الأمامية."], "Only if cart has items": ["فقط في حال كانت عربة التسوق تحتوي على عناصر"], "Always (even if empty)": ["دائمًا (حتى في حال كانت فارغة)"], "Show Cart Item Count:": ["إظهار عدد العناصر في عربة التسوق:"], "Product Count": ["<PERSON><PERSON><PERSON> المنتجات"], "Cart Icon": ["أيقونة عربة التسوق"], "Icon": ["أيقونة"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["قم بالتبديل لفتح درج عربة التسوق الصغيرة عندما يضيف المتسوق منتجًا إلى عربة التسوق الخاصة به."], "Open drawer when adding": ["فتح درج عند الإضافة"], "Behavior": ["سلوك"], "Edit Mini-Cart Drawer template": ["تحرير قالب درج عربة التسوق الصغيرة"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["عند فتحها، يمنح درج عربة التسوق الصغيرة المتسوقين وصولاً سريعًا لعرض منتجاتهم المحددة والسداد."], "Cart Drawer": ["درج عربة التسوق"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["قم بالتبديل لعرض إجمالي سعر المنتجات في عربة التسوق. في حال عدم إضافة أي منتجات، فلن يُعرض السعر."], "Display total price": ["عرض السعر الإجمالي"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["حدد الكيفية التي تتصرف بها عربة التسوق الصغيرة في صفحتي عربة التسوق والسداد. قد يؤثر ذلك في تخطيط الترويسة."], "Mini-Cart in cart and checkout pages": ["عربة التسوق الصغيرة في صفحتَي عربة التسوق والسداد"], "Hide": ["إخفاء"], "Display": ["العرض"], "Never": ["أبدًا"], "Price": ["السعر"], "Remove": ["إزالة"], "Settings": ["الإعدادات"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}