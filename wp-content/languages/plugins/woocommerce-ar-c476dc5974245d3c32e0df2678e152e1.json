{"translation-revision-date": "2024-12-17 15:38:43+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "productGalleryClientId and clientId codes mismatch.": ["أكواد productGalleryClientId and clientId غير متطابقة."], "Open pop-up when clicked": ["فتح نافذة منبثقة عند النقر"], "Gallery Navigation": ["التنقل في المعرض"], "Pager": ["أداة الاستدعاء"], "No pager will be displayed.": ["لن تُعرض أداة الاستدعاء."], "A series of dots will show to indicate the number of items.": ["ستظهر مجموعة من النقاط للإشارة إلى عدد العناصر."], "A list of numbers will show to indicate the number of items.": ["ستظهر قائمة من الأرقام للإشارة إلى عدد العناصر."], "Next/Prev Buttons": ["الأزرار التالية/السابقة"], "No next or previous button will be displayed.": ["لن يُعرض زر تالٍ أو سابق."], "Next and previous buttons will appear on outside the large image.": ["ستظهر الأزرار التالية والسابقة خارج الصورة الكبيرة."], "Next and previous buttons will appear inside the large image.": ["ستظهر الأزرار التالية والسابقة داخل الصورة الكبيرة."], "Clicking on the large image will open a full-screen gallery experience.": ["سيؤدي النقر على الصورة الكبيرة إلى فتح تجربة المعرض بشاشة كاملة."], "While hovering the large image will zoom in by 30%.": ["في أثناء تحويم الصورة الكبيرة، سيتم تكبيرها بمعدل 30%."], "Zoom while hovering": ["التكبير/التصغير في أثناء التحويم"], "Images will be cropped to fit within a square space.": ["سيتم قص الصور لملاءمة المساحة المربعة."], "Crop images to fit": ["قص الصور للملاءمة"], "Media Settings": ["إعدادات الوسائط"], "Choose how many thumbnails (3-8) will display. If more images exist, a “View all” button will display.": ["اختر عدد الصور المصغَّرة (من 3 إلى 8) الذي سيُعرض. في حال وجود مزيد من الصور، سيظهر زر \"عرض الكل\"."], "Number of Thumbnails": ["ع<PERSON><PERSON> الصور المصغّرة"], "Off": ["<PERSON>ي<PERSON><PERSON><PERSON> الت<PERSON>غيل"], "Thumbnails": ["الصور المصغّرة"], "A strip of small images will appear to the right of the main gallery image.": ["سيظهر شريط من الصور الصغيرة على يمين صورة المعرض الأساسية."], "A strip of small images will appear below the main gallery image.": ["سيظهر شريط من الصور الصغيرة أسفل صورة المعرض الأساسية."], "A strip of small images will appear to the left of the main gallery image.": ["سيظهر شريط من الصور الصغيرة على يسار صورة المعرض الرئيسية."], "No thumbnails will be displayed.": ["لن تُعرض أي صور مصغّرة."], "An error has prevented the block from being updated.": ["أدى حدوث خطأ ما إلى منع تحديث المكوّن."], "The following error was returned": ["تم إرجاع الخطأ التالي"], "Retry": ["إعادة المحاولة"], "Sorry, an error occurred": ["عذرًا، حدث خطأ"], "The following error was returned from the API": ["تم إرجاع الخطأ التالي من واجهة برمجة التطبيقات"]}}, "comment": {"reference": "assets/client/blocks/product-gallery.js"}}