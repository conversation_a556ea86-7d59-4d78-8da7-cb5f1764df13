{"translation-revision-date": "2024-12-17 15:38:43+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Local pickup #2": ["الاستلام المحلي رقم 2"], "Local pickup #1": ["الاستلام المحلي رقم 1"], "The quantity of \"%1$s\" was changed to %2$d.": ["تم تغيير كمية \"⁦%1$s⁩\" إلى ⁦%2$d⁩."], "\"%s\" was removed from your cart.": ["تمت إزالة \"%s\" من عربة التسوق الخاصة بك."], "Flat rate shipping": ["الشحن بأسعار موحدة"], "T-Shirt": ["تيشيرت"], "Hoodie with Pocket": ["رداء مزوّد بقلنسوة وجيبَين"], "Hoodie with Logo": ["رداء مزوّد بقلنسوة عليه شعار"], "Hoodie with Zipper": ["رداء مزوّد بقلنسوة وسحاب"], "Long Sleeve Tee": ["قميص بأكمام طويلة"], "Polo": ["بولو"], "%s (optional)": ["%s (اختياري)"], "There was an error registering the payment method with id '%s': ": ["حدث خطأ في أثناء تسجيل طريقة الدفع باستخدام المعرِّف \"%s\":"], "Orange": ["برتقالي"], "Lightweight baseball cap": ["قبعة بيسبول خفيفة الوزن"], "Cap": ["قبعة"], "Yellow": ["أصفر"], "Warm hat for winter": ["قبعة تدفئة للشتاء"], "Beanie": ["قبعة صغيرة"], "example product in Cart Block\u0004Beanie": ["قبعة صغيرة"], "example product in Cart Block\u0004Beanie with Logo": ["قبعة صغيرة مطبوع عليها شعار"], "Something went wrong. Please contact us to get assistance.": ["هناك خطأ ما. يُرجى التواصل معنا للحصول على مساعدة."], "Unable to get cart data from the API.": ["يتعذر الحصول على بيانات سلة المشتريات من واجهة برمجة التطبيقات."], "The response is not a valid JSON response.": ["الاستجابة ليست استجابة JSON صالحة."], "Sales tax": ["ضريبة المبيعات"], "Color": ["لون"], "Small": ["صغير"], "Size": ["الحجم"], "Free shipping": ["<PERSON><PERSON><PERSON>اني"], "Shipping": ["الشحن"], "Fee": ["رسوم"]}}, "comment": {"reference": "assets/client/blocks/wc-blocks-data.js"}}