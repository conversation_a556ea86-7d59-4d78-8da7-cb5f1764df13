{"translation-revision-date": "2024-12-17 15:38:43+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "This will affect product category pages": ["سيؤثر ذلك في صفحات تصنيف المنتجات"], "Only show children of current category": ["عرض مجموعة فرعية من التصنيفات الحالية فقط"], "Category images are hidden.": ["صور التصنيف مخفية."], "Category images are visible.": ["صور التصنيف مرئية."], "Show category images": ["عرض صور التصنيف"], "This block displays the product categories for your store. To use it you first need to create a product and assign it to a category.": ["يعرض هذا المكوّن تصنيفات المنتجات لمتجرك. لاستخدامه، يتعين عليك أولاً إنشاء منتج وتعيينه إلى تصنيف."], "Display style": ["أسلوب العرض"], "List Settings": ["إعدادات القائمة"], "Show empty categories": ["إظهار التصنيفات الفارغة"], "Show product count": ["إظها<PERSON> عدد المنتجات"], "Product Categories List": ["قائمة تصنيفات المنتجات"], "Show hierarchy": ["عرض التسلسل الهرمي"], "Dropdown": ["قائمة منسدلة"], "List": ["قائمة"], "Content": ["المحتوى"]}}, "comment": {"reference": "assets/client/blocks/product-categories.js"}}