# Translation of Plugins - YITH WooCommerce Wishlist - Stable (latest release) in Arabic
# This file is distributed under the same license as the Plugins - YITH WooCommerce Wishlist - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-03-11 06:43:37+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: ar\n"
"Project-Id-Version: Plugins - YITH WooCommerce Wishlist - Stable (latest release)\n"

#: plugin-fw/templates/panel/help-tab.php:129
msgctxt "Help tab Read Documentation link"
msgid "to learn how the plugin works from the basics."
msgstr "لمعرفة كيفية عمل الإضافة من الأساسيات."

#: plugin-fw/templates/panel/help-tab.php:142
msgctxt "Help tab Watch video tutorials link"
msgid "to see some helpful use cases."
msgstr "لمشاهدة بعض حالات الاستخدام المفيدة."

#: plugin-fw/templates/panel/premium-tab-content.php:62
msgctxt "Premium Tab"
msgid "Check the free vs premium features >"
msgstr "تحقق من مميزات المجانية vs بريميوم >"

#: plugin-fw/templates/panel/premium-tab-content.php:102
msgctxt "Premium Tab"
msgid "Get the premium version"
msgstr "الحصول على نسخة Premium"

#: plugin-fw/includes/class-yit-plugin-panel.php:977
msgctxt "Premium tab name"
msgid "Get premium"
msgstr "الحصول على Premium"

#: plugin-fw/templates/sysinfo/tabs/db-info.php:24
msgid "Database Info"
msgstr "معلومات قاعدة البيانات"

#: plugin-fw/templates/sysinfo/tabs/db-info.php:28
msgid "MySQL version"
msgstr "إصدار MySQL"

#. Translators: %s: Codex link.
#: plugin-fw/templates/sysinfo/tabs/db-info.php:19
msgid "WordPress recommends a minimum MySQL version of 5.6. See: %s"
msgstr "يوصي ووردبريس بإصدار 5.6 كحد أدنى من MySQL. شاهد: %s"

#. Translators: %s: Codex link.
#: plugin-fw/templates/sysinfo/tabs/db-info.php:19
msgid "WordPress requirements"
msgstr "متطلبات ووردبريس"

#: plugin-fw/templates/sysinfo/tabs/db-info.php:34
msgid "Total Database Size"
msgstr "إجمالي حجم قاعدة البيانات"

#: plugin-fw/templates/sysinfo/tabs/db-info.php:38
msgid "Database Data Size"
msgstr "حجم قاعدة البيانات"

#: plugin-fw/templates/sysinfo/tabs/db-info.php:42
msgid "Database Index Size"
msgstr "حجم فهرس قاعدة البيانات"

#: plugin-fw/templates/sysinfo/tabs/db-info.php:46
msgid "Database Free Size"
msgstr "حجم المساحة الحرة لقاعدة البيانات"

#. Translators: %1$f: Table size, %2$f: Index size, %3$f: Free size, %4$s
#. Engine.
#: plugin-fw/templates/sysinfo/tabs/db-info.php:57
msgid "Data: %1$.2fMB | Index: %2$.2fMB | Free: %3$.2fMB | Engine: %4$s"
msgstr "البيانات: %1$.2fMB | الفهرس: %2$.2fMB: المساحة الحرة: %3$.2fMB | المحرك: %4$s"

#: plugin-fw/templates/sysinfo/tabs/main.php:61
msgid "YITH Plugin Framework Version"
msgstr "نسخة إطار عمل الإضافة (YITH Plugin Framework)"

#. translators: %s is the name of the plugin that is loading the framework.
#: plugin-fw/templates/sysinfo/tabs/main.php:66
msgid "loaded by %s"
msgstr "تم التحميل بواسطة %s"

#: plugin-fw/templates/panel/help-tab.php:204
msgctxt "Help tab submit ticket button"
msgid "Submit a ticket"
msgstr "تقديم تذكرة"

#: plugin-fw/templates/panel/help-tab.php:193
msgctxt "Help tab submit ticket title"
msgid "Need help?"
msgstr "تحتاج إلى مساعدة؟"

#: plugin-fw/templates/panel/help-tab.php:184
msgctxt "Help tab FAQ link"
msgid "View all FAQs >"
msgstr "عرض كل الأسئلة الشائعة >"

#: plugin-fw/templates/panel/help-tab.php:170
msgctxt "Help tab FAQ title"
msgid "Last FAQs in our Help Center"
msgstr "آخر الأسئلة الشائعة في مركز المساعدة الخاص بنا"

#: plugin-fw/templates/panel/help-tab.php:152
msgctxt "Help tab view FAQs link"
msgid "Check the FAQs"
msgstr "تحقَّق من الأسئلة الشائعة"

#: plugin-fw/templates/panel/help-tab.php:126
msgctxt "Help tab Read Documentation link"
msgid "Read the documentation"
msgstr "إقرأ وثائق المستندات"

#: plugin-fw/templates/panel/help-tab.php:96
msgctxt "Help tab Watch Videotutorials link"
msgid "Videos are also available in:"
msgstr "تتوفر مقاطع الفيديو أيضًا بـ:"

#. translators: 1. Url to EN playlist.
#: plugin-fw/templates/panel/help-tab.php:89
msgctxt "Help tab view all video link"
msgid "Check the full <a href=\"%s\" target=\"_blank\">Playlist on Youtube</a> to learn more >"
msgstr "تحقق من <a href=\"%s\" target=\"_blank\">قائمة التشغيل الكاملة على Youtube</a> لمعرفة المزيد >"

#: plugin-fw/templates/fields/image-dimensions.php:23
msgctxt "Image height field label"
msgid "Height"
msgstr "الارتفاع"

#: plugin-fw/templates/fields/image-dimensions.php:18
msgctxt "Image width field label"
msgid "Width"
msgstr "العرض"

#. translators: 1. Plugin name.
#: plugin-fw/includes/class-yit-plugin-panel.php:920
msgctxt "Help tab default title"
msgid "Thank you for purchasing %s!"
msgstr "شكرًا لك لشراء %s!"

#: plugin-fw/includes/class-yit-plugin-panel.php:1007
msgctxt "Help tab name"
msgid "Help"
msgstr "مساعدة"

#: plugin-fw/includes/class-yit-help-desk.php:148
msgid "There was an error with your request; please try again later."
msgstr "ثمَّة خطأ في طلبك. يُرجى المحاولة لاحقاً."

#: plugin-fw/includes/class-yit-assets.php:241
msgid "Are you sure you want to delete the selected items?"
msgstr "هل تريد بالتأكيد حذف هذه العناصر المحدَّدة؟"

#: plugin-fw/includes/class-yit-assets.php:238 plugin-fw/yit-functions.php:1996
msgctxt "Trash confirmation action"
msgid "Yes, move to trash"
msgstr "نعم، نقل إلى سلة المهملات"

#: plugin-fw/includes/class-yit-assets.php:237
msgid "Are you sure you want to trash the selected items?"
msgstr "هل تريد بالتأكيد نقل هذه العناصر المحدَّدة إلى سلة المهملات؟"

#: includes/admin/class-yith-wcwl-admin-panel.php:139
msgctxt "[HELP TAB] Video title"
msgid "Check this video to learn how to <b>configure wishlist and customize options:</b>"
msgstr "شاهد هذا الفيديو للتعرّف على كيفية <b>تكوين قائمة الرغبات وتخصيص الخيارات:</b>"

#: plugin-fw/templates/panel/help-tab.php:196
msgctxt "Help tab submit ticket description"
msgid "If you are experiencing any technical issues, ask for help from our developers. Submit a ticket through our support desk and we will help you as soon as possible."
msgstr "إذا كنت تواجه أي مشكلات فنية، فاطلب المساعدة من مطوّرينا. أرسل تذكرة خلال نظام الدعم الخاص بنا وسنساعدك في أقرب وقت ممكن."

#: plugin-fw/templates/panel/help-tab.php:155
msgctxt "Help tab view FAQs link"
msgid "to find answers to your doubts."
msgstr "للعثور على إجابات لتساؤولاتك."

#: plugin-fw/templates/panel/help-tab.php:139
msgctxt "Help tab Watch video tutorials link"
msgid "Watch our video tutorials"
msgstr "شاهد دروس الفيديو لدينا"

#: plugin-fw/includes/class-yit-plugin-panel.php:929
msgctxt "Help tab default description"
msgid "We want to help you enjoy a wonderful experience with all of our products."
msgstr "نريد مساعدتك للاستمتاع بتجربة رائعة مع جميع منتجاتنا."

#: plugin-fw/includes/class-yit-assets.php:224
msgctxt "Button text"
msgid "Confirm"
msgstr "تأكيد"

#: plugin-fw/includes/class-yit-assets.php:225
msgctxt "Button text"
msgid "Cancel"
msgstr "إلغاء"

#. translators: %s is the name of the post type (example Back to "Membership
#. Plans").
#: plugin-fw/includes/class-yith-post-type-admin.php:294
msgid "Back to \"%s\""
msgstr "العودة إلى \"%s\""

#. translators: %s is the name of the post type (example Back to "Membership
#. Plans").
#: plugin-fw/includes/class-yith-post-type-admin.php:294
msgid "Back to the list"
msgstr "العودة إلى القائمة"

#. translators: %s is the title of the post object.
#: plugin-fw/yit-functions.php:1925
msgid "Are you sure you want to move \"%s\" to trash?"
msgstr "هل تريد بالتأكيد نقل \"%s\" إلى سلة المهملات؟"

#. translators: %s is the title of the post object.
#: plugin-fw/yit-functions.php:1927 plugin-fw/yit-functions.php:2086
msgid "Are you sure you want to delete \"%s\"?"
msgstr "هل تريد بالتأكيد حذف \"%s\"؟"

#: plugin-fw/yit-functions.php:1939
msgctxt "Post action"
msgid "Preview"
msgstr "معاينة"

#: plugin-fw/yit-functions.php:1948
msgctxt "Post action"
msgid "View"
msgstr "عرض"

#: plugin-fw/yit-functions.php:1959
msgctxt "Post action"
msgid "Edit"
msgstr "تحرير"

#: plugin-fw/yit-functions.php:1967
msgctxt "Post action"
msgid "Duplicate"
msgstr "استنساخ"

#: plugin-fw/yit-functions.php:1979
msgctxt "Post action"
msgid "Restore"
msgstr "استعادة"

#: plugin-fw/yit-functions.php:1987
msgctxt "Post action"
msgid "Trash"
msgstr "سلة المهملات"

#: plugin-fw/includes/class-yit-assets.php:236 plugin-fw/yit-functions.php:1993
msgid "Confirm trash"
msgstr "تأكيد الحذف إلى سلة المهملات"

#: plugin-fw/yit-functions.php:2004
msgctxt "Post action"
msgid "Delete Permanently"
msgstr "حذف بشكل دائم"

#: plugin-fw/includes/class-yit-assets.php:240 plugin-fw/yit-functions.php:2011
#: plugin-fw/yit-functions.php:2138
msgid "Confirm delete"
msgstr "تأكيد الحذف"

#: plugin-fw/includes/class-yit-assets.php:242 plugin-fw/yit-functions.php:2014
#: plugin-fw/yit-functions.php:2140
msgctxt "Delete confirmation action"
msgid "Yes, delete"
msgstr "نعم، حذف"

#: plugin-fw/yit-functions.php:2025 plugin-fw/yit-functions.php:2033
#: plugin-fw/yit-functions.php:2149
msgid "Further actions"
msgstr "إجراءات إضافية"

#: plugin-fw/yit-functions.php:2100
msgctxt "Term action"
msgid "View"
msgstr "عرض"

#: plugin-fw/yit-functions.php:2110
msgctxt "Term action"
msgid "Edit"
msgstr "تحرير"

#: plugin-fw/yit-functions.php:2118
msgctxt "Term action"
msgid "Duplicate"
msgstr "استنساخ"

#: plugin-fw/yit-functions.php:2131
msgctxt "Term action"
msgid "Delete"
msgstr "حذف"

#: plugin-fw/templates/panel/v2/panel-content-page.php:57
#: plugin-fw/templates/panel/v2/woocommerce/woocommerce-form.php:69
#: plugin-fw/templates/panel/woocommerce/woocommerce-form.php:27
#: plugin-fw/templates/panel/woocommerce/woocommerce-form.php:31
msgid "Save Options"
msgstr "حفظ الخيارات"

#: plugin-fw/templates/panel/woocommerce/woocommerce-form.php:31
msgid "Options Saved"
msgstr "تم حفظ الخيارات"

#. translators: %s is the title of the post object.
#: plugin-fw/includes/class-yit-assets.php:241 plugin-fw/yit-functions.php:1927
#: plugin-fw/yit-functions.php:2086
msgid "This action cannot be undone and you will not be able to recover this data."
msgstr "لا يمكن التراجع عن هذا الإجراء ولن تتمكن من استعادة هذه البيانات."

#. translators: %s it the Elementor Widget title.
#: plugin-fw/includes/builders/elementor/class-yith-elementor-widget.php:252
msgctxt "Elementor Widget - section title"
msgid "%s - Options"
msgstr "%s - الخيارات"

#: plugin-fw/templates/panel/panel-header.php:26
#: plugin-fw/templates/panel/v2/panel-header.php:21
msgid "help us by leaving a good review"
msgstr "ساعدنا من خلال ترك مراجعة جيدة"

#: plugin-fw/templates/fields/copy-to-clipboard.php:41
msgctxt "Copy-to-clipboard message"
msgid "Copied!"
msgstr "تم النسخ!"

#: plugin-fw/templates/fields/copy-to-clipboard.php:45
msgctxt "Copy-to-clipboard button text"
msgid "Copy"
msgstr "نسخ"

#: plugin-fw/templates/fields/date-format.php:70
msgid "Preview:"
msgstr "معاينة:"

#: plugin-fw/includes/class-yit-pointers.php:97
msgid "From now on, you can find the option panel of YITH plugins in YITH menu. Every time one of our plugins is added, a new entry will be added to this menu. For example, after the update, plugin options (such as for YITH WooCommerce Wishlist, YITH WooCommerce Ajax Search, etc.) will be moved from previous location to YITH menu."
msgstr "من الآن فصاعدًا، يمكنك العثور على لوحة خيارات إضافة YITH في قائمة YITH. في كل مرة يتم فيها إضافة أحد الإضافات البرمجية الخاصة بنا، سيتم إضافة إدخال جديد إلى هذه القائمة. على سبيل المثال، بعد التحديث، سيتم نقل خيارات الإضافة (مثل YITH WooCommerce Wishlist و YITH WooCommerce Ajax Search وإلخ ) من المكان السابق إلى قائمة YITH."

#: plugin-fw/includes/class-yit-pointers.php:81
msgid "From now on, you can find all plugin options in YITH menu. Plugin customization settings will be available as a new entry in YITH menu."
msgstr "من الآن فصاعدًا، يمكنك العثور على جميع خيارات الإضافة في قائمة YITH. ستكون إعدادات تخصيص الإضافة متاحة كإدخال جديد في قائمة YITH."

#. translators: 1. YITH site link; 2. WordPress site link.
#: plugin-fw/includes/class-yit-pointers.php:84
#: plugin-fw/includes/class-yit-pointers.php:100
msgid "Discover all our plugins available on: %1$s and %2$s"
msgstr "اكتشف كل الإضافات المتوفرة لدينا على: %1$s و %2$s"

#: plugin-fw/templates/sysinfo/tabs/error-log.php:59
msgid "The file size exceeds 8 megabytes so it must be downloaded"
msgstr "يتجاوز حجم الملف 8 ميجابايت لذا يجب تنزيله"

#: plugin-fw/templates/sysinfo/tabs/error-log.php:95
msgid "Copied!"
msgstr "تم النسخ!"

#: plugin-fw/templates/sysinfo/tabs/error-log.php:95
msgid "Copy Code"
msgstr "نسخ الرمز"

#. translators: %s file name.
#: plugin-fw/templates/sysinfo/tabs/error-log.php:86
msgid "No Log file available. Enable the WordPress debug by adding this in the %s file of your installation"
msgstr "لا يوجد ملف سجل متاح. قم بتمكين وضع اكتشاف وتصحيح أخطاء ووردبريس عن طريق إضافة هذا في ملف %s الخاص بالتنصيب"

#: plugin-fw/includes/class-yith-system-status.php:191
msgid "PHPInfo"
msgstr "معلومات PHP"

#: plugin-fw/includes/class-yith-system-status.php:199
#: plugin-fw/templates/sysinfo/tabs/error-log.php:108
msgid "Log Files"
msgstr "ملفات السجل"

#: plugin-fw/templates/sysinfo/tabs/main.php:41
msgid "Site Info"
msgstr "معلومات الموقع"

#: plugin-fw/templates/sysinfo/tabs/main.php:72
msgid "Plugins Requirements"
msgstr "متطلبات الإضافات"

#: plugin-fw/templates/sysinfo/tabs/error-log.php:48
msgid "Download"
msgstr "تنزيل"

#: plugin-fw/templates/sysinfo/tabs/main.php:57
msgid "External object cache"
msgstr "عنصر تخزين مؤقّت (cache) خارجي"

#. translators: 1. Count of users when many, or "another" when only one.
#: includes/functions-yith-wcwl.php:383
msgid "another"
msgstr "آخر"

#: plugin-fw/includes/class-yit-assets.php:273
msgid "Clear"
msgstr "مسح"

#: plugin-fw/includes/class-yit-assets.php:274
msgid "Clear color"
msgstr "مسح اللون"

#: plugin-fw/includes/class-yit-assets.php:275
msgid "Default"
msgstr "الافتراضي"

#: plugin-fw/includes/class-yit-assets.php:276
msgid "Select default color"
msgstr "تحديد اللون الافتراضي"

#: plugin-fw/includes/class-yit-assets.php:277
msgid "Select Color"
msgstr "تحديد اللون"

#: plugin-fw/includes/class-yit-assets.php:278
msgid "Color value"
msgstr "قيمة اللون"

#: plugin-fw/includes/class-yith-system-status.php:563
msgid "Contact your hosting company in order to install it."
msgstr "الاتصال بشركة الاستضافة الخاصة بك لتثبيتها."

#: plugin-fw/templates/sysinfo/tabs/error-log.php:13
msgid "WP debug.log file"
msgstr "ملف WP debug.log"

#: plugin-fw/templates/sysinfo/tabs/error-log.php:18
msgid "PHP error_log file"
msgstr "ملف PHP error_log"

#: plugin-fw/templates/sysinfo/tabs/main.php:53
msgid "Defined WP_CACHE"
msgstr "محدد WP_CACHE"

#: plugin-fw/templates/sysinfo/tabs/main.php:54
#: plugin-fw/templates/sysinfo/tabs/main.php:58
msgid "Yes"
msgstr "نعم"

#: plugin-fw/includes/class-yit-assets.php:239
#: plugin-fw/includes/class-yit-assets.php:243
#: plugin-fw/templates/sysinfo/tabs/main.php:54
#: plugin-fw/templates/sysinfo/tabs/main.php:58
#: plugin-fw/yit-functions.php:1995 plugin-fw/yit-functions.php:2013
msgid "No"
msgstr "لا"

#. translators: %1$s TLS label, %2$s cURL label
#: plugin-fw/includes/class-yith-system-status.php:621
msgid "The system check cannot determine which %1$s version is installed because %2$s module is disabled. Ask your hosting company to enable it."
msgstr "لا يمكن لفحص النظام تحديد أي إصدار %1$s تم تثبيته؛ لأن الوحدة %2$s معطلة. اطلب من شركة الاستضافة الخاصة بك تمكينها."

#. translators: %1$s TLS label
#: plugin-fw/includes/class-yith-system-status.php:624
msgid "The system check cannot determine which %1$s version is installed due to a connection issue between your site and our server."
msgstr "لا يمكن لفحص النظام تحديد إصدار %1$s تم تثبيته؛ بسبب مشكلة في الاتصال بين موقعك وسيرفرنا."

#: plugin-fw/templates/fields/dimensions.php:94
msgctxt "Tooltip in the \"Dimensions\" field"
msgid "Link values together"
msgstr "ربط القيم معًا"

#: plugin-fw/templates/fields/dimensions.php:15
msgctxt "Position in the \"Dimensions\" field"
msgid "Bottom"
msgstr "أسفل"

#: plugin-fw/templates/fields/dimensions.php:13
msgctxt "Position in the \"Dimensions\" field"
msgid "Top"
msgstr "أعلى"

#: plugin-options/lists-options.php:41
msgid "Search list"
msgstr "قائمة البحث"

#: includes/class-yith-wcwl-add-to-wishlist-button.php:886
#: includes/class-yith-wcwl-privacy.php:315
#: includes/class-yith-wcwl-shortcode.php:262
#: includes/class-yith-wcwl-wishlist.php:392
#: includes/data-stores/class-yith-wcwl-wishlist-data-store.php:1079
#: plugin-options/customization/labels-options.php:79
msgid "My wishlist"
msgstr "قائمة رغباتي"

#: includes/class-yith-wcwl-ajax-handler.php:94
msgid "Close"
msgstr "إغلاق"

#: includes/admin/class-yith-wcwl-admin.php:67
msgid "Wishlist Page"
msgstr "صفحة قائمة الرغبات"

#: plugin-fw/templates/fields/dimensions.php:14
msgctxt "Position in the \"Dimensions\" field"
msgid "Right"
msgstr "يمين"

#: plugin-fw/templates/fields/dimensions.php:16
msgctxt "Position in the \"Dimensions\" field"
msgid "Left"
msgstr "يسار"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:175
msgctxt "Elementor control label"
msgid "Icon for the button"
msgstr "أيقونة للزر"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:157
msgctxt "Elementor section title"
msgid "Advanced"
msgstr "متقدم"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:165
msgctxt "Elementor control label"
msgid "URL of the wishlist page"
msgstr "رابط URL لصفحة قائمة الرغبات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:185
msgctxt "Elementor control label"
msgid "Additional CSS classes for the button"
msgstr "فئات CSS إضافية للزر"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:148
msgid "Product added to wishlist"
msgstr "تم إضافة المنتج لقائمة الرغبات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:45
msgctxt "Elementor widget name"
msgid "YITH Wishlist"
msgstr "قائمة الرغبات YITH"

#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:87
msgctxt "Elementor section title"
msgid "Wishlist"
msgstr "قائمة الرغبات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:95
msgctxt "Elementor control label"
msgid "Wishlist ID"
msgstr "معرّف قائمة الرغبات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:107
msgctxt "Elementor section title"
msgid "Pagination"
msgstr "تعدد الصفحات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:115
msgctxt "Elementor control label"
msgid "Paginate items"
msgstr "ترقيم العناصر"

#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:128
msgctxt "yith-woocommerce-wishlist"
msgid "Items per page"
msgstr "عنصر لكل صفحة"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:45
msgctxt "Elementor widget name"
msgid "YITH Wishlist Add button"
msgstr "إضافة YITH زر إضافة قائمة الرغبات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:87
msgctxt "Elementor section title"
msgid "Product"
msgstr "المنتج"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:95
msgctxt "Elementor control label"
msgid "Product ID"
msgstr "معرف المنتج"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:107
msgctxt "Elementor section title"
msgid "Labels"
msgstr "التسميات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:115
msgctxt "Elementor control label"
msgid "Button label"
msgstr "تسمية الزر"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:135
msgctxt "Elementor control label"
msgid "\"Product already in wishlist\" label"
msgstr "تسمية \"المنتج موجود بالفعل في قائمة الرغبات\""

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:138
msgid "Product already in wishlist"
msgstr "المنتج موجود بالفعل في قائمة الرغبات"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:145
msgctxt "Elementor control label"
msgid "\"Product added to wishlist\" label"
msgstr "تسمية \"تم إضافة المنتج لقائمة الرغبات\""

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:125
msgctxt "Elementor control label"
msgid "\"Browse wishlist\" label"
msgstr "تسمية \"تصفُّح قائمة الرغبات\""

#: includes/functions-yith-wcwl.php:603
msgid "Custom"
msgstr "مُخصص"

#: plugin-fw/templates/panel/panel-header.php:23
#: plugin-fw/templates/panel/v2/panel-header.php:18
msgid "We need your support"
msgstr "نحتاج إلى دعمك"

#: plugin-fw/templates/panel/panel-header.php:24
#: plugin-fw/templates/panel/v2/panel-header.php:19
msgid "to keep updating and improving the plugin. Please,"
msgstr "لمواصلة التحديثات والتحسينات للإضافة. يرجى،"

#: plugin-fw/templates/panel/panel-header.php:27
#: plugin-fw/templates/panel/v2/panel-header.php:22
msgid ":) Thanks!"
msgstr ":) شكرًا!"

#: includes/class-yith-wcwl-ajax-handler.php:93
#: templates/add-to-wishlist-remove.php:67
msgid "or"
msgstr "أو"

#: includes/class-yith-wcwl-ajax-handler.php:92
#: templates/add-to-wishlist-remove.php:79
msgid "View &rsaquo;"
msgstr "عرض &rsaquo;"

#: templates/wishlist-view-mobile.php:143
msgid "Added on:"
msgstr "تمت الإضافة في:"

#: templates/wishlist-view-mobile.php:154
msgid "Price:"
msgstr "السعر:"

#: templates/wishlist-view-mobile.php:181
msgid "Quantity:"
msgstr "الكمية:"

#: templates/wishlist-view-mobile.php:196
msgid "Stock:"
msgstr "المخزون:"

#: plugin-options/customization/style-options.php:504
msgid "Share button text color"
msgstr "لون نص زر المشاركة"

#: plugin-options/customization/style-options.php:505
msgid "Choose colors for share buttons text"
msgstr "اختيار الألوان لنص أزرار المشاركة"

#: plugin-options/customization/style-options.php:525
msgid "Select an icon for the Facebook share button"
msgstr "تحديد أيقونة لزر المشاركة على Facebook"

#: plugin-options/customization/style-options.php:538
msgid "Facebook share button custom icon"
msgstr "أيقونة زر مخصصة للمشاركة على Facebook"

#: plugin-options/customization/style-options.php:539
msgid "Upload an icon you'd like to use for Facebook share button (suggested 32px x 32px)"
msgstr "رفع أيقونة تودّ استخدامها لزر المشاركة على Facebook (المقترح 32px × 32px)"

#: plugin-options/customization/style-options.php:627
msgid "Select an icon for the Pinterest share button"
msgstr "تحديد أيقونة لزر المشاركة على Pinterest"

#: plugin-options/customization/style-options.php:640
msgid "Pinterest share button custom icon"
msgstr "أيقونة زر مخصصة للمشاركة على Pinterest"

#: plugin-options/customization/style-options.php:641
msgid "Upload an icon you'd like to use for Pinterest share button (suggested 32px x 32px)"
msgstr "رفع أيقونة تودّ استخدامها لزر المشاركة على Pinterest (المقترح 32px × 32px)"

#: plugin-options/customization/style-options.php:653
msgid "Pinterest share button style"
msgstr "نمط زر مشاركة على Pinterest"

#: plugin-options/customization/style-options.php:654
msgid "Choose colors for Pinterest share button"
msgstr "اختيار الألوان لزر مشاركة Pinterest"

#: plugin-options/customization/style-options.php:673
msgid "Email share button icon"
msgstr "أيقونة زر المشاركة عبر البريد الإلكتروني"

#: plugin-options/customization/style-options.php:674
msgid "Select an icon for the Email share button"
msgstr "تحديد أيقونة لزر المشاركة عبر البريد الإلكتروني"

#: plugin-options/customization/style-options.php:687
msgid "Email share button custom icon"
msgstr "أيقونة زر مخصصة للمشاركة عبر البريد الإلكتروني"

#: plugin-options/customization/style-options.php:688
msgid "Upload an icon you'd like to use for the Email share button (suggested 32px x 32px)"
msgstr "رفع أيقونة تودّ استخدامها لزر المشاركة عبر البريد الإلكتروني (المقترح 32px × 32px)"

#: plugin-options/customization/style-options.php:700
msgid "Email share button style"
msgstr "نمط زر المشاركة عبر البريد الإلكتروني"

#: plugin-options/customization/style-options.php:701
msgid "Choose colors for the Email share button"
msgstr "اختيار الألوان لزر المشاركة عبر البريد الإلكتروني"

#: plugin-options/customization/style-options.php:720
msgid "WhatsApp share button icon"
msgstr "أيقونة زر مشاركة عبر WhatsApp"

#: plugin-options/customization/style-options.php:721
msgid "Select an icon for the WhatsApp share button"
msgstr "تحديد أيقونة لزر المشاركة عبر WhatsApp"

#: plugin-options/customization/style-options.php:734
msgid "WhatsApp share button custom icon"
msgstr "أيقونة زر مخصصة للمشاركة عبر WhatsApp"

#: plugin-options/customization/style-options.php:735
msgid "Upload an icon you'd like to use for WhatsApp share button (suggested 32px x 32px)"
msgstr "رفع أيقونة تودّ استخدامها لزر المشاركة عبر WhatsApp (المقترح 32px × 32px)"

#: plugin-options/customization/style-options.php:747
msgid "WhatsApp share button style"
msgstr "نمط زر المشاركة عبر WhatsApp"

#: plugin-options/customization/style-options.php:551
msgid "Facebook share button style"
msgstr "نمط زر المشاركة على Facebook"

#: plugin-options/customization/style-options.php:524
msgid "Facebook share button icon"
msgstr "أيقونة زر المشاركة على Facebook"

#: plugin-options/customization/style-options.php:552
msgid "Choose colors for Facebook share button"
msgstr "اختيار الألوان لزر المشاركة على Facebook"

#: plugin-options/customization/style-options.php:626
msgid "Pinterest share button icon"
msgstr "أيقونة زر المشاركة على Pinterest"

#: plugin-options/customization/style-options.php:748
msgid "Choose colors for WhatsApp share button"
msgstr "اختيار الألوان لزر المشاركة عبر WhatsApp"

#: templates/share.php:111
msgid "Whatsapp"
msgstr "WhatsApp"

#: templates/wishlist-view-footer-mobile.php:55
#: templates/wishlist-view-footer.php:55
msgid "Apply this action to all the selected items:"
msgstr "تطبيق هذا الإجراء على جميع العناصر المحددة:"

#: templates/wishlist-view-footer-mobile.php:73
#: templates/wishlist-view-footer.php:73
msgid "Remove from wishlist"
msgstr "إزالة من قائمة الرغبات"

#. translators: 1. Wishlist formatted name.
#: templates/wishlist-view-footer-mobile.php:93
#: templates/wishlist-view-footer.php:93
msgid "Move to %s"
msgstr "نقل إلى %s"

#: templates/wishlist-view-footer-mobile.php:99
#: templates/wishlist-view-footer.php:99
msgid "Apply"
msgstr "تطبيق"

#: templates/wishlist-view-footer-mobile.php:107
#: templates/wishlist-view-footer.php:107
msgid "Update"
msgstr "تحديث"

#: templates/wishlist-view-footer-mobile.php:113
#: templates/wishlist-view-footer.php:151
msgid "Add all to cart"
msgstr "إضافة الكل إلى السلة"

#: templates/wishlist-view.php:143
msgid "Quantity"
msgstr "الكمية"

#: templates/wishlist-view-mobile.php:263 templates/wishlist-view.php:575
msgid "Move to another list &rsaquo;"
msgstr "نقل إلى قائمة أخرى &rsaquo;"

#: templates/wishlist-view.php:206
msgid "Arrange"
msgstr "ترتيب"

#: plugin-options/customization/style-options.php:492
msgid "Choose the color for all sections with background<br/><small>This color will be used as background for the wishlist table heading and footer (when set to \"Traditional\" layout), and for various form across wishlist views</small>"
msgstr "اختر اللون لجميع الأقسام ذات الخلفية<br/><small>سيتم استخدام هذا اللون كخلفية لعنوان ترويسة جدول قائمة الرغبات وتذييله (عند التعيين على التخطيط \"التقليدي\")، وللأنواع عبر عروض قائمة الرغبات</small>"

#: plugin-options/customization/style-options.php:413
msgid "Secondary button style"
msgstr "نمط الزر الثانوي"

#: plugin-options/customization/style-options.php:462
msgid "Wishlist table style"
msgstr "نمط / تنسيق جدول قائمة الرغبات"

#: plugin-options/customization/style-options.php:491
msgid "Highlight color"
msgstr "لون التمييز"

#: plugin-options/customization/style-options.php:370
msgid "Choose colors for the primary button<br/><small>This style will be applied to \"Edit title\" button on Wishlist view, \"Submit Changes\" button on Manage view and \"Search wishlist\" button on Search view</small>"
msgstr "اختر ألوان الزر الأساسي<br/><small>سيتم تطبيق هذا النمط على زر \"تحرير العنوان\" أثناء عرض قائمة الرغبات، وزرّ \"إرسال التغييرات\" أثناء عرض الإدارة وزرّ \"البحث في قوائم الرغبات\" أثناء عرض البحث</small>"

#: plugin-options/customization/style-options.php:388
msgid "Choose colors for the primary button on hover state<br/><small>This style will be applied to \"Edit title\" button on Wishlist view, \"Submit Changes\" button on Manage view and \"Search wishlist\" button on Search view</small>"
msgstr "اختر ألوان الزر الأساسي<br/><small>سيتم تطبيق هذا النمط على حالة مؤثر تمرير الفأرة لـ زر \"تحرير العنوان\" أثناء عرض قائمة الرغبات، وزرّ \"إرسال التغييرات\" أثناء عرض الإدارة وزرّ \"البحث في قوائم الرغبات\" أثناء عرض البحث</small>"

#: plugin-options/customization/style-options.php:463
msgid "Choose the colors for the wishlist table (when set to \"Traditional\" layout)"
msgstr "اختيار ألوان جدول قائمة الرغبات (عند التعيين على التنسيق \"التقليدي\")"

#: plugin-options/customization/style-options.php:419
#: plugin-options/customization/style-options.php:437
msgid "Choose colors of the secondary button<br/><small>This style will be applied to the buttons that allow showing and hiding the Edit title form on Wishlist view and \"Create new Wishlist\" button on Manage view</small>"
msgstr "اختيار ألوان الزر الثانوي<br/><small>سيتم تطبيق هذا النمط على الأزرار التي تسمح بإظهار وإخفاء نموذج تحرير العنوان في عرض قائمة الرغبات وزر \"إنشاء قائمة أمنيات جديدة\" أثناء عرض الإدارة</small>"

#: plugin-options/customization/labels-options.php:26
msgid "Enter a text for \"Add to wishlist\" button"
msgstr "إضافة نص لـ زر \"إضافة إلى قائمة الرغبات\""

#: plugin-options/customization/labels-options.php:34
msgid "Enter the text of the message displayed when the user adds a product to the wishlist"
msgstr "أدخل نص الرسالة المعروضة عندما يضيف المستخدم منتجًا إلى قائمة الرغبات"

#: plugin-options/customization/labels-options.php:58
msgid "Enter the text for the message displayed when the user views a product that is already in the wishlist"
msgstr "أدخل نص الرسالة المعروضة عندما يعرض المستخدم منتجًا موجودًا بالفعل في قائمة الرغبات"

#: plugin-options/customization/style-options.php:31
msgid "Style of \"Add to wishlist\""
msgstr "نمط \"إضافة إلى قائمة الرغبات\""

#: plugin-options/customization/style-options.php:36
#: plugin-options/customization/style-options.php:253
msgid "Button with theme style"
msgstr "زر بنمط وتنسيق القالب"

#: plugin-options/customization/style-options.php:37
#: plugin-options/customization/style-options.php:254
msgid "Button with custom style"
msgstr "زر بنمط وتنسيق مخصص"

#: plugin-options/customization/style-options.php:45
msgid "\"Add to wishlist\" button style"
msgstr "نمط زر \"إضافة إلى قائمة الرغبات\""

#: plugin-options/customization/style-options.php:32
msgid "Choose if you want to show a textual \"Add to wishlist\" link or a button"
msgstr "اختيار ما إذا كنت تريد إظهار زر أو رابط نصي لـ \"إضافة إلى قائمة الرغبات\""

#: plugin-options/customization/style-options.php:35
#: plugin-options/customization/style-options.php:252
msgid "Textual (anchor)"
msgstr "رابط نصي (روابط القفز anchor)"

#: plugin-options/customization/style-options.php:74
#: plugin-options/customization/style-options.php:293
#: plugin-options/customization/style-options.php:395
#: plugin-options/customization/style-options.php:444
msgid "Text Hover"
msgstr "مؤثر تمرير الفأرة للنص"

#: plugin-options/customization/style-options.php:79
#: plugin-options/customization/style-options.php:298
#: plugin-options/customization/style-options.php:400
#: plugin-options/customization/style-options.php:449
msgid "Border Hover"
msgstr "مؤثر تمرير الفأرة للإطار"

#: plugin-options/customization/style-options.php:93
msgid "Choose radius for the \"Add to wishlist\" button"
msgstr "اختيار زوايا الحدود / الإطار لزر \"إضافة إلى قائمة الرغبات\""

#: plugin-options/customization/style-options.php:92
#: plugin-options/customization/style-options.php:311
msgid "Border radius"
msgstr "زوايا الإطار (الحدود)"

#: plugin-options/settings/wishlist_page-options.php:56
#: plugin-options/settings/wishlist_page-options.php:65
#: plugin-options/settings/wishlist_page-options.php:74
#: plugin-options/settings/wishlist_page-options.php:83
#: plugin-options/settings/wishlist_page-options.php:92
#: plugin-options/settings/wishlist_page-options.php:101
#: plugin-options/settings/wishlist_page-options.php:110
msgid "In wishlist table show"
msgstr "في عرض جدول قائمة الرغبات"

#: plugin-options/settings/wishlist_page-options.php:146
#: plugin-options/settings/wishlist_page-options.php:155
#: plugin-options/settings/wishlist_page-options.php:164
#: plugin-options/settings/wishlist_page-options.php:173
#: plugin-options/settings/wishlist_page-options.php:182
msgid "Share on social media"
msgstr "مشاركة على وسائل التواصل الاجتماعي"

#: plugin-options/customization/labels-options.php:84
msgid "Sharing title"
msgstr "عنوان المشاركة"

#: plugin-options/customization/style-options.php:262
msgid "\"Add to cart\" button style"
msgstr "نمط زر \"إضافة إلى السلة\""

#: plugin-options/customization/style-options.php:268
msgid "Choose the colors for the \"Add to cart\" button"
msgstr "اختيار الألوان لزر \"إضافة إلى السلة\""

#: plugin-options/customization/style-options.php:286
msgid "Choose colors for the \"Add to cart\" button on hover state"
msgstr "اختيار ألوان الزر \"إضافة إلى السلة\" في حالة مؤثر تمرير الماوس"

#: plugin-options/customization/style-options.php:312
msgid "Set the radius for the \"Add to cart\" button"
msgstr "ضبط زوايا الحدود / الإطار لزر \"إضافة إلى السلة\""

#: plugin-options/customization/style-options.php:327
msgid "Select an icon for the \"Add to cart\" button (optional)"
msgstr "تحديد أيقونة للزر \"إضافة إلى السلة\" (اختياري)"

#: plugin-options/customization/style-options.php:351
msgid "\"Add to cart\" custom icon"
msgstr "أيقونة مخصصة لـ \"إضافة إلى السلة\""

#: plugin-options/customization/style-options.php:352
msgid "Upload an icon you'd like to use for the \"Add to cart\" button (suggested 32px x 32px)"
msgstr "رفع أيقونة تودّ استخدامها للزر \"إضافة إلى السلة\" (المقترح 32px × 32px)"

#: plugin-options/customization/style-options.php:364
msgid "Primary button style"
msgstr "نمط الزر الأساسي"

#: plugin-options/customization/style-options.php:121
msgid "Select an icon for the \"Add to wishlist\" button (optional)"
msgstr "تحديد أيقونة للزر \"إضافة إلى قائمة الرغبات\" (اختياري)"

#: plugin-options/customization/style-options.php:156
msgid "\"Add to wishlist\" custom icon"
msgstr "أيقونة مخصصة لـ \"إضافة إلى قائمة الرغبات\""

#: plugin-options/customization/style-options.php:157
#: plugin-options/customization/style-options.php:208
msgid "Upload an icon you'd like to use for \"Add to wishlist\" button (suggested 32px x 32px)"
msgstr "رفع أيقونة تودّ استخدامها للزر \"إضافة إلى قائمة المشتريات\" (المقترح 32px × 32px)"

#: plugin-options/customization/style-options.php:170
#: plugin-options/customization/style-options.php:184
msgid "Select an icon for the \"Added to wishlist\" button (optional)"
msgstr "تحديد أيقونة للزر \"تمت الإضافة إلى قائمة الرغبات\" (اختياري)"

#: plugin-options/customization/style-options.php:176
msgid "Same used for Add to wishlist"
msgstr "نفسها المستخدمة لـ إضافة إلى قائمة الرغبات"

#: plugin-options/customization/style-options.php:207
msgid "\"Added to wishlist\" custom icon"
msgstr "أيقونة مخصصة لـ \"تمت الإضافة إلى قائمة الرغبات\""

#: plugin-options/settings/wishlist_page-options.php:66
msgid "Product price"
msgstr "سعر المنتج"

#: plugin-options/settings/wishlist_page-options.php:75
msgid "Product stock (show if the product is available or not)"
msgstr "مخزون المنتج (إظهار ما إذا كان المنتج متاحًا أم لا)"

#: plugin-options/settings/wishlist_page-options.php:84
msgid "Date on which the product was added to the wishlist"
msgstr "التاريخ الذي تم فيه إضافة المنتج إلى قائمة الرغبات"

#: plugin-options/settings/wishlist_page-options.php:93
msgid "Add to cart option for each product"
msgstr "إضافة خيار (إضافة إلى السلة) لكل منتج"

#: plugin-options/settings/wishlist_page-options.php:102
msgid "Icon to remove the product from the wishlist - to the left of the product"
msgstr "أيقونة لإزالة المنتج من قائمة الرغبات - على يسار المنتج"

#: plugin-options/settings/wishlist_page-options.php:111
msgid "Button to remove the product from the wishlist - to the right of the product"
msgstr "أيقونة لإزالة المنتج من قائمة الرغبات - على يمين المنتج"

#: plugin-options/settings/wishlist_page-options.php:137
msgid "Share wishlist"
msgstr "مشاركة قائمة الرغبات"

#: plugin-options/settings/wishlist_page-options.php:138
msgid "Enable this option to let users share their wishlist on social media"
msgstr "تمكين هذا الخيار للسماح للمستخدمين بمشاركة قائمة رغباتهم على وسائل التواصل الاجتماعي"

#: plugin-options/settings/wishlist_page-options.php:120
msgid "Redirect users to the cart page when they add a product to the cart from the wishlist page"
msgstr "إعادة توجيه المستخدمين إلى صفحة سلة المشتريات عندما يضيفون منتجًا إلى السلة من صفحة قائمة الرغبات"

#: plugin-options/customization/labels-options.php:85
msgid "Wishlist title used for sharing (only used on Twitter and Pinterest)"
msgstr "عنوان قائمة الرغبات يستخدم للمشاركة (يستخدم فقط على Twitter و Pinterest)"

#: plugin-options/customization/labels-options.php:94
msgid "Type the message you want to publish when you share your wishlist on Twitter and Pinterest"
msgstr "اكتب الرسالة التي تريد نشرها عندما تشارك قائمة رغباتك على Twitter و Pinterest"

#: plugin-options/customization/labels-options.php:77
msgid "Enter a name for the default wishlist. This is the wishlist that will be automatically generated for all users if they do not create any custom one"
msgstr "أدخل اسمًا لقائمة الرغبات الافتراضية. هذه هي قائمة الرغبات التي سيتم إنشاؤها تلقائيًا لجميع المستخدمين إذا لم يقوموا بإنشاء أي قائمة مخصصة"

#: plugin-options/customization/labels-options.php:103
msgid "Enter a text for the \"Add to cart\" button"
msgstr "إضافة نص لـ زر \"إضافة إلى السلة\""

#: plugin-options/customization/style-options.php:248
msgid "Style of \"Add to cart\""
msgstr "نمط \"إضافة إلى السلة\""

#: plugin-options/customization/style-options.php:249
msgid "Choose whether to show a textual \"Add to cart\" link or a button"
msgstr "اختيار ما إذا كنت تريد إظهار زر أو رابط نصي لـ \"إضافة إلى السلة\""

#: plugin-options/settings/wishlist_page-options.php:57
msgid "Product variations selected by the user (example: size or color)"
msgstr "اختلافات (أنواع/ تباينات) المنتج التي حددها المستخدم (مثال: الحجم أو اللون)"

#: plugin-options/customization/style-options.php:229
msgid "Enter custom CSS to be applied to Wishlist elements (optional)"
msgstr "أدخل CSS مخصص لتطبيقه على عناصر قائمة الرغبات (اختياري)"

#: plugin-options/settings/wishlist_page-options.php:36
msgid "Pick a page as the main Wishlist page; make sure you add the <span class=\"code\"><code>[yith_wcwl_wishlist]</code></span> shortcode into the page content"
msgstr "اختر صفحة كصفحة قائمة الرغبات الرئيسية؛ تأكد من إضافة الكود القصير <span class=\"code\"><code>[yith_wcwl_wishlist]</code></span> في محتوى الصفحة"

#: plugin-options/settings/add_to_wishlist-options.php:104
msgid "Choose where to show \"Add to wishlist\" button or link on the product page. <span class=\"addon\">Copy this shortcode <span class=\"code\"><code>[yith_wcwl_add_to_wishlist]</code></span> and paste it where you want to show the \"Add to wishlist\" link or button</span>"
msgstr "اختيار مكان إظهار الزر \"إضافة إلى قائمة الرغبات\" أو الرابط الموجود في صفحة المنتج. <span class=\"addon\">انسخ هذا الكود القصير <span class=\"code\"><code>[yith_wcwl_add_to_wishlist]</code></span> وألصقه حيث تريد إظهار رابط أو زر \"إضافة إلى قائمة الرغبات\"</span>"

#: plugin-options/customization/labels-options.php:50
msgid "Enter a text for the \"Browse wishlist\" link on the product page"
msgstr "أدخل نصًا لرابط \"تصفُّح قائمة الرغبات\" في صفحة المنتج"

#: plugin-options/settings/add_to_wishlist-options.php:79
msgid "On top of the image"
msgstr "في أعلى الصورة"

#: plugin-options/settings/add_to_wishlist-options.php:80
msgid "Before \"Add to cart\" button"
msgstr "قبل زر \"إضافة إلى السلة\""

#: plugin-options/settings/add_to_wishlist-options.php:81
msgid "After \"Add to cart\" button"
msgstr "بعد زر \"إضافة إلى السلة\""

#: plugin-options/settings/add_to_wishlist-options.php:96
msgid "Product page settings"
msgstr "إعدادات صفحة المنتج"

#: plugin-options/settings/add_to_wishlist-options.php:103
msgid "Position of \"Add to wishlist\" on product page"
msgstr "موضع \"إضافة إلى قائمة الرغبات\" على صفحة المنتج"

#: plugin-options/settings/add_to_wishlist-options.php:72
msgid "Choose where to show \"Add to wishlist\" button or link in WooCommerce products' loop. <span class=\"addon\">Copy this shortcode <span class=\"code\"><code>[yith_wcwl_add_to_wishlist]</code></span> and paste it where you want to show the \"Add to wishlist\" link or button</span>"
msgstr "اختيار مكان إظهار الزر \"إضافة إلى قائمة الرغبات\" أو الرابط الموجود في تكرار منتجات WooCommerce. <span class=\"addon\">انسخ هذا الكود القصير <span class=\"code\"><code>[yith_wcwl_add_to_wishlist]</code></span> وألصقه حيث تريد إظهار رابط أو زر \"إضافة إلى قائمة الرغبات\"</span>"

#: includes/class-yith-wcwl-shortcode.php:532
msgid "Share via email"
msgstr "مشاركة عبر البريد الإلكتروني"

#: includes/class-yith-wcwl-add-to-wishlist-button.php:874
#: includes/class-yith-wcwl-add-to-wishlist-button.php:878
#: plugin-options/customization/labels-options.php:44
msgid "Remove from list"
msgstr "إزالة من القائمة"

#: includes/class-yith-wcwl-privacy.php:272
msgid "Wishlist URL"
msgstr "رابط URL قائمة الرغبات"

#: includes/data-stores/class-yith-wcwl-wishlist-item-data-store.php:212
msgid "Invalid wishlist item."
msgstr "عنصر قائمة الرغبات غير صالح."

#: includes/data-stores/class-yith-wcwl-wishlist-data-store.php:252
#: includes/data-stores/class-yith-wcwl-wishlist-data-store.php:274
msgid "Invalid wishlist."
msgstr "قائمة الرغبات غير صالحة."

#: includes/class-yith-wcwl-form-handler.php:193
msgid "Please, make sure to enter a valid title"
msgstr "من فضلك، تأكد من إدخال عنوان صحيح"

#: plugin-options/settings/general-options.php:38
msgid "Enable AJAX loading"
msgstr "تفعيل تحميل AJAX loading"

#: plugin-options/settings/add_to_wishlist-options.php:40
msgid "Show \"Add to wishlist\" button"
msgstr "إظهار زر \"إضافة إلى قائمة الرغبات\""

#: plugin-options/settings/add_to_wishlist-options.php:41
msgid "Show \"View wishlist\" link"
msgstr "إظهار رابط \"عرض قائمة الرغبات\""

#: plugin-options/settings/add_to_wishlist-options.php:42
msgid "Show \"Remove from list\" link"
msgstr "إظهار رابط \"إزالة من قائمة الرغبات\""

#: plugin-options/settings/add_to_wishlist-options.php:55
msgid "Loop settings"
msgstr "إعدادات التكرار (Loop)"

#: plugin-options/settings/add_to_wishlist-options.php:62
msgid "Show \"Add to wishlist\" in loop"
msgstr "إظهار \"إضافة إلى قائمة الرغبات\" في التكرار"

#: plugin-options/settings/add_to_wishlist-options.php:63
msgid "Enable the \"Add to wishlist\" feature in WooCommerce products' loop"
msgstr "تمكين ميزة \"إضافة إلى قائمة الرغبات\" في حلقة تكرار منتجات WooCommerce"

#: plugin-options/settings/add_to_wishlist-options.php:71
msgid "Position of \"Add to wishlist\" in loop"
msgstr "موضع \"إضافة إلى قائمة الرغبات\" في حلقة التكرار"

#: plugin-options/settings/add_to_wishlist-options.php:36
msgid "After product is added to wishlist"
msgstr "بعد إضافة المنتج إلى قائمة الرغبات"

#: includes/class-yith-wcwl-shortcode.php:66
msgid "URL of the wishlist page (leave empty to use the default settings)"
msgstr "رابط URL لصفحة قائمة الرغبات (اتركه فارغاً لاستخدام الإعدادات الافتراضية)"

#: includes/class-yith-wcwl-shortcode.php:71
msgid "Button label (leave empty to use the default settings)"
msgstr "تسمية الزر (اتركه فارغاً لاستخدام الإعدادات الافتراضية)"

#: includes/class-yith-wcwl-shortcode.php:96
msgid "Additional CSS classes for the button (leave empty to use the default settings)"
msgstr "فئات CSS إضافية للزر (اتركه فارغاً لاستخدام الإعدادات الافتراضية)"

#: plugin-options/settings/add_to_wishlist-options.php:37
msgid "Choose the look of the Wishlist button when the product has already been added to a wishlist"
msgstr "اختيار مظهر زر قائمة الرغبات عندما يكون المنتج قد تمت إضافته بالفعل إلى قائمة الرغبات"

#: plugin-options/settings/add_to_wishlist-options.php:57
msgid "Loop options will be visible on Shop page, category pages, product shortcodes, products sliders, and all the other places where the WooCommerce products' loop is used"
msgstr "ستكون خيارات التكرار مرئية في صفحة المتجر، وصفحات التصنيفات، والأكواد القصيرة للمنتجات، وشرائح المنتجات، وجميع الأماكن الأخرى التي يتم فيها استخدام حلقة منتجات WooCommerce"

#: plugin-options/settings/general-options.php:39
msgid "Load any cacheable wishlist item via AJAX"
msgstr "تحميل أي عنصر بقائمة الرغبات قابل للتخزين المؤقت عبر AJAX"

#: includes/class-yith-wcwl-shortcode.php:76
msgid "\"Browse wishlist\" label (leave empty to use the default settings)"
msgstr "تسمية \"تصفُّح قائمة الرغبات\" (اتركه فارغاً لاستخدام الإعدادات الافتراضية)"

#: includes/class-yith-wcwl-frontend.php:723
#: includes/legacy/class-yith-wcwl-frontend-legacy.php:258
msgid "Product added to cart successfully"
msgstr "تمت إضافة المنتج إلى سلة المشتريات بنجاح"

#: plugin-options/settings/add_to_wishlist-options.php:29
#: plugin-options/settings/general-options.php:32
msgid "General settings"
msgstr "الإعدادات العامة"

#: includes/class-yith-wcwl-wishlists.php:277
msgid "The item cannot be added to this wishlist"
msgstr "لا يمكن إضافة العنصر إلى قائمة الرغبات هذه"

#. translators: 1. Number of users.
#: includes/functions-yith-wcwl.php:375
msgid "%d user"
msgid_plural "%d users"
msgstr[0] "%d مستخدم"
msgstr[1] "مستخدم واحد (%d)"
msgstr[2] "مستخدمان (%d)"
msgstr[3] "%d مستخدمين"
msgstr[4] "%d مستخدم"
msgstr[5] "%d مستخدم"

#. translators: 1. Number of users.
#: includes/functions-yith-wcwl.php:376
msgid "has this item in wishlist"
msgid_plural "have this item in wishlist"
msgstr[0] "لديه هذا العنصر في قائمة الرغبات"
msgstr[1] "لديه هذا العنصر في قائمة الرغبات"
msgstr[2] "لديهما هذا العنصر في قائمة الرغبات"
msgstr[3] "لديهم هذا العنصر في قائمة الرغبات"
msgstr[4] "لديهم هذا العنصر في قائمة الرغبات"
msgstr[5] "لديهم هذا العنصر في قائمة الرغبات"

#: includes/functions-yith-wcwl.php:378
msgid "You're the first"
msgstr "أنت الأول"

#: includes/functions-yith-wcwl.php:379
msgid "to add this item in wishlist"
msgstr "لإضافة هذا العنصر في قائمة الرغبات"

#. translators: 1. Count of users when many, or "another" when only one.
#: includes/functions-yith-wcwl.php:383
msgid "You and %s user"
msgid_plural "You and %d users"
msgstr[0] "أنت و %d مستخدم"
msgstr[1] "أنت ومستخدم واحد (%s)"
msgstr[2] "أنت و %d مستخدمين"
msgstr[3] "أنت و %d مستخدمين"
msgstr[4] "أنت و %d مستخدم"
msgstr[5] "أنت و %d مستخدم"

#. translators: 1. Count of users when many, or "another" when only one.
#: includes/functions-yith-wcwl.php:384
msgid "have this item in wishlist"
msgstr "لديهم هذا العنصر في قائمة الرغبات"

#: includes/admin/class-yith-wcwl-admin-panel.php:92
msgid "Allows your customers to create and share lists of products that they want to purchase on your e-commerce."
msgstr "يسمح لعملائك بإنشاء ومشاركة قوائم المنتجات التي يرغبون في شرائها من خلال منصة التجارة الإلكترونية الخاصة بك."

#: includes/class-yith-wcwl-frontend.php:713
#: includes/legacy/class-yith-wcwl-frontend-legacy.php:248
msgid "We are sorry, but this feature is available only if cookies on your browser are enabled."
msgstr "نعتذر منك، ولكن هذه الميزة متاحة فقط إذا تم تمكين ملفات تعريف الارتباط على متصفحك."

#. translators: 1. % of reduction/increase in price.
#: includes/class-yith-wcwl-wishlist-item.php:488
msgctxt "Part of the template that shows price variation since addition to list; placeholder will be replaced with a percentage"
msgid "Price is %1$s%%"
msgstr "السعر %1$s%%"

#. translators: 2: original product price.
#: includes/class-yith-wcwl-wishlist-item.php:490
msgctxt "Part of the template that shows price variation since addition to list; placeholder will be replaced with a price"
msgid "(Was %2$s when added  in list)"
msgstr "(كان %2$s عند إضافته إلى القائمة)"

#: plugin-fw/templates/sysinfo/tabs/main.php:45
msgid "Site URL"
msgstr "رابط الموقع URL"

#: plugin-fw/templates/sysinfo/tabs/main.php:49
msgid "Output IP Address"
msgstr "ناتج عنوان IP"

#: plugin-fw/includes/class-yith-system-status.php:522
msgid "N/A"
msgstr "N/A"

#. translators: 1. user display name; 2. user ID; 3. user email.
#: plugin-fw/templates/fields/ajax-customers.php:68
#: plugin-fw/templates/fields/ajax-customers.php:79
msgid "%1$s (#%2$s &ndash; %3$s)"
msgstr "%1$s (#%2$s &ndash; %3$s)"

#: plugin-fw/includes/class-yith-system-status.php:144
msgid "Iconv Module"
msgstr "وحدة Iconv"

#: templates/share.php:120
msgid "(Now"
msgstr "(الآن"

#: templates/share.php:120
msgid "copy"
msgstr "نسخ"

#: plugin-options/settings/wishlist_page-options.php:191
msgid "Share by URL"
msgstr "مشاركة رابط URL"

#: plugin-options/settings/wishlist_page-options.php:192
msgid "Show \"Share URL\" field on wishlist page"
msgstr "إظهار حقل \"مشاركة رابط URL\" في صفحة قائمة الرغبات"

#: templates/share.php:120
msgid "this wishlist link and share it anywhere)"
msgstr "رابط قائمة الرغبات هذا ومشاركته في أي مكان)"

#: plugin-fw/includes/class-yith-system-status.php:518
msgid "Enabled"
msgstr "مُفعّل"

#: plugin-fw/includes/class-yith-system-status.php:518
msgid "Disabled"
msgstr "مُعطّل"

#: plugin-fw/includes/class-yith-system-status.php:161
#: plugin-fw/includes/class-yith-system-status.php:162
#: plugin-fw/includes/class-yith-system-status.php:187
msgid "System Status"
msgstr "حالة النظام"

#: plugin-fw/includes/class-yith-system-status.php:134
msgid "WordPress Version"
msgstr "إصدار ووردبريس"

#: plugin-fw/includes/class-yith-system-status.php:135
msgid "WooCommerce Version"
msgstr "إصدار WooCommerce"

#: plugin-fw/includes/class-yith-system-status.php:136
msgid "Available Memory"
msgstr "الذاكرة المتاحة"

#: plugin-fw/includes/class-yith-system-status.php:137
msgid "PHP Version"
msgstr "إصدار PHP"

#: plugin-fw/includes/class-yith-system-status.php:225
msgid "WooCommerce"
msgstr "ووكومرس WooCommerce"

#: plugin-fw/includes/class-yith-system-status.php:368
msgid "Warning!"
msgstr "تحذير!"

#: plugin-fw/templates/sysinfo/system-information-panel.php:27
msgid "YITH System Information"
msgstr "معلومات نظام YITH"

#. translators: %1$s plugin name, %2$s requirement name
#: plugin-fw/includes/class-yith-system-status.php:543
msgid "%1$s needs %2$s enabled"
msgstr "تتطلب %1$s تفعيل %2$s"

#. translators: %1$s plugin name, %2$s required memory amount
#: plugin-fw/includes/class-yith-system-status.php:546
msgid "%1$s needs at least %2$s of available memory"
msgstr "تحتاج %1$s إلى %2$s على الأقل من الذاكرة المتوفرة"

#. translators: %1$s code, %2$s file name
#: plugin-fw/includes/class-yith-system-status.php:570
msgid "Remove %1$s from %2$s file"
msgstr "إزالة %1$s من ملف %2$s"

#: plugin-fw/includes/class-yith-system-status.php:559
#: plugin-fw/includes/class-yith-system-status.php:565
msgid "Contact your hosting company in order to update it."
msgstr "الاتصال بشركة الاستضافة الخاصة بك لتحديثها."

#: plugin-fw/includes/class-yith-system-status.php:138
msgid "TLS Version"
msgstr "نسخة TLS"

#: plugin-fw/includes/class-yith-system-status.php:139
msgid "WordPress Cron"
msgstr "ووردبريس Cron"

#: plugin-fw/includes/class-yith-system-status.php:140
msgid "SimpleXML"
msgstr "SimpleXML"

#: plugin-fw/includes/class-yith-system-status.php:141
msgid "MultiByte String"
msgstr "سلسلة MultiByte"

#: plugin-fw/includes/class-yith-system-status.php:142
msgid "ImageMagick Version"
msgstr "نسخة ImageMagick"

#: plugin-fw/includes/class-yith-system-status.php:143
msgid "GD Library"
msgstr "مكتبة GD"

#: plugin-fw/includes/class-yith-system-status.php:145
msgid "OPCache Save Comments"
msgstr "OPCache حفظ التعليقات"

#: plugin-fw/includes/class-yith-system-status.php:146
msgid "URL FOpen"
msgstr "URL FOpen"

#. translators: %1$s plugin name, %2$s version number
#: plugin-fw/includes/class-yith-system-status.php:549
msgid "%1$s needs at least %2$s version"
msgstr "%1$s بحاجة إلى النسخة %2$s على الأقل"

#: plugin-fw/includes/class-yith-system-status.php:555
msgid "Update it to the latest version in order to benefit of all new features and security updates."
msgstr "تحديثها إلى أحدث نسخة للاستفادة من جميع الميزات الجديدة والتحديثات الأمنية."

#: plugin-fw/includes/class-yith-system-status.php:578
msgid "Contact your hosting company in order to enable it."
msgstr "الاتصال بشركة الاستضافة الخاصة بك لتفعيلها."

#. translators: %s recommended memory amount
#: plugin-fw/includes/class-yith-system-status.php:613
msgid "For optimal functioning of our plugins, we suggest setting at least %s of available memory"
msgstr "للحصول على الأداء الأمثل لإضافاتنا البرمجية، نقترح تعيين %s على الأقل من الذاكرة المتوفرة"

#. translators: %1$s open link tag, %2$s open link tag
#: plugin-fw/includes/class-yith-system-status.php:372
msgid "The system check has detected some compatibility issues on your installation.%1$sClick here%2$s to know more"
msgstr "اكتشف فحص النظام بعض مشكلات التوافق في التنصيب الخاص بك. %1$sأنقر هنا%2$s لمعرفة المزيد"

#. translators: %1$s opening link tag, %2$s closing link tag
#: plugin-fw/includes/class-yith-system-status.php:582
#: plugin-fw/includes/class-yith-system-status.php:616
msgid "Read more %1$shere%2$s or contact your hosting company in order to increase it."
msgstr "اقرأ المزيد %1$sهنا%2$s أو اتصل بشركة الاستضافة الخاصة بك من أجل زيادتها."

#: plugin-fw/templates/fields/select-buttons.php:19
msgid "Add All"
msgstr "إضافة الكل"

#: plugin-fw/templates/fields/select-buttons.php:20
msgid "Remove All"
msgstr "إزالة الكل"

#: plugin-fw/templates/fields/date-format.php:65
msgid "Custom:"
msgstr "مخصص:"

#: plugin-fw/includes/class-yith-dashboard.php:137
msgctxt "Button label"
msgid "Close"
msgstr "إغلاق"

#: includes/class-yith-wcwl-shortcode.php:538
#: plugin-options/settings/wishlist_page-options.php:183
msgid "Share on WhatsApp"
msgstr "مشاركة على WhatsApp"

#: templates/share.php:110
msgid "WhatsApp"
msgstr "WhatsApp"

#: plugin-fw/includes/class-yith-dashboard.php:35
msgid "YITH Latest Updates"
msgstr "آخر تحديثات YITH"

#: plugin-fw/includes/class-yith-dashboard.php:36
msgid "Latest news from YITH Blog"
msgstr "أحدث الأخبار من مدونة YITH"

#: plugin-fw/includes/class-yith-dashboard.php:56
msgid "RSS Error:"
msgstr "خطأ RSS:"

#: plugin-fw/includes/class-yith-dashboard.php:63
msgid "An error has occurred, which probably means the feed is down. Try again later."
msgstr "حدث خطأ ما، مما يعني أن الخلاصة معطلة. حاول مجدداً لاحقاً."

#: plugin-fw/includes/class-yith-dashboard.php:96
msgctxt "Plugin FW"
msgid "View Changelog"
msgstr "عرض سجلّ التغييرات"

#: plugin-fw/includes/class-yith-dashboard.php:97
msgctxt "Plugin FW"
msgid "Latest update released on"
msgstr "صدر آخر تحديث في"

#: plugin-fw/includes/builders/gutenberg/class-yith-gutenberg.php:156
msgctxt "[gutenberg]: Category Name"
msgid "YITH"
msgstr "YITH"

#: includes/class-yith-wcwl-shortcode.php:55
msgctxt "[gutenberg]: block name"
msgid "YITH Add to wishlist"
msgstr "YITH إضافة إلى قائمة الرغبات"

#: includes/class-yith-wcwl-shortcode.php:56
msgctxt "[gutenberg]: block description"
msgid "Shows Add to wishlist button"
msgstr "يظهر زر إضافة إلى قائمة الرغبات"

#: includes/class-yith-wcwl-shortcode.php:104
msgctxt "[gutenberg]: block name"
msgid "YITH Wishlist"
msgstr "قائمة الرغبات YITH"

#: includes/class-yith-wcwl-shortcode.php:105
msgctxt "[gutenberg]: block description"
msgid "Shows a list of products in wishlist"
msgstr "يظهر قائمة من المنتجات في قائمة الرغبات"

#: includes/class-yith-wcwl-shortcode.php:113
#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:118
msgid "Paginate"
msgstr "ترقيم الصفحات"

#: includes/class-yith-wcwl-shortcode.php:114
#: includes/widgets/elementor/class-yith-wcwl-elementor-wishlist.php:119
msgid "Do not paginate"
msgstr "عدم ترقيم الصفحات"

#: includes/class-yith-wcwl-shortcode.php:119
msgid "Number of items to show per page"
msgstr "عدد العناصر لإظهارها في كل صفحة"

#: includes/class-yith-wcwl-shortcode.php:124
msgid "ID of the wishlist to show (e.g. K6EOWXB888ZD)"
msgstr "معرّف قائمة الرغبات المراد عرضها (مثل K6EOWXB888ZD)"

#: init.php:156
msgid "is enabled but not effective. It requires WooCommerce to work."
msgstr "تم تفعيلها ولكنها غير نشطة. يتطلب WooCommerce للعمل."

#: includes/class-yith-wcwl-shortcode.php:61
msgid "ID of the product to add to the wishlist (leave empty to use the global product)"
msgstr "معرّف المنتج المراد إضافته إلى قائمة الرغبات (اتركه فارغاً لاستخدام المنتج العالمي)"

#: includes/class-yith-wcwl-shortcode.php:81
msgid "\"Product already in wishlist\" label (leave empty to use the default settings)"
msgstr "تسمية \"المنتج موجود بالفعل في قائمة الرغبات\" (اتركه فارغاً لاستخدام الإعدادات الافتراضية)"

#: includes/class-yith-wcwl-shortcode.php:86
msgid "\"Product added to wishlist\" label (leave empty to use the default settings)"
msgstr "تسمية \"تم إضافة المنتج لقائمة الرغبات\" (اتركه فارغاً لاستخدام الإعدادات الافتراضية)"

#: includes/class-yith-wcwl-shortcode.php:91
msgid "Icon for the button (use any FontAwesome valid class, or leave empty to use the default settings)"
msgstr "أيقونة للزر (استخدام أي class صالح لـ FontAwesome، أو اتركها فارغة لاستخدام الإعدادات الافتراضية)"

#: includes/class-yith-wcwl-shortcode.php:110
msgid "Choose whether to paginate items in the wishlist or show them all"
msgstr "اختيار ما إذا كنت تريد ترقيم الصفحات في قائمة الرغبات أو إظهارها جميعًا"

#. Description of the plugin
#: init.php
msgid "<code><strong>YITH WooCommerce Wishlist</strong></code> gives your users the possibility to create, fill, manage and share their wishlists allowing you to analyze their interests and needs to improve your marketing strategies. <a href=\"https://yithemes.com/\" target=\"_blank\">Get more plugins for your e-commerce on <strong>YITH</strong></a>"
msgstr "تتيح <code><strong>YITH WooCommerce Wishlist</strong></code> للمستخدمين إمكانية إنشاء قوائم رغباتهم وملؤها وإدارتها ومشاركتها مما يتيح لك تحليل اهتماماتهم واحتياجاتهم لتحسين استراتيجيات التسويق الخاصة بك. <a href=\"https://yithemes.com/\" target=\"_blank\">احصل على المزيد من الإضافات لتجارتك الإلكترونية على موقع <strong>YITH</strong></a>"

#: plugin-fw/yit-plugin.php:86
msgctxt "Plugin Row Meta"
msgid "Live Demo"
msgstr "عرض توضيحي مباشر"

#: plugin-fw/yit-plugin.php:90
msgctxt "Plugin Row Meta"
msgid "Documentation"
msgstr "وثائق المساعدة"

#: plugin-fw/yit-plugin.php:94
msgctxt "Plugin Row Meta"
msgid "Support"
msgstr "الدعم الفني"

#: plugin-fw/yit-plugin.php:98
msgctxt "Plugin Row Meta"
msgid "Premium version"
msgstr "النسخة المدفوعة"

#: plugin-fw/yit-plugin.php:239
msgctxt "Action links"
msgid "Settings"
msgstr "الإعدادات"

#: plugin-fw/yit-plugin.php:243
msgid "License"
msgstr "الترخيص"

#. Author of the plugin
#: init.php
msgid "YITH"
msgstr "YITH"

#: plugin-fw/includes/privacy/class-yith-privacy.php:106
msgctxt "Privacy Policy Content"
msgid "Payments"
msgstr "المدفوعات"

#: plugin-fw/includes/privacy/class-yith-privacy.php:103
msgctxt "Privacy Policy Content"
msgid "What we share with others"
msgstr "ما نشاركه مع الآخرين"

#: plugin-fw/includes/privacy/class-yith-privacy.php:100
msgctxt "Privacy Policy Content"
msgid "Who on our team has access"
msgstr "من لديه حق الوصول من فريقنا"

#: plugin-fw/includes/privacy/class-yith-privacy.php:97
msgctxt "Privacy Policy Content"
msgid "What we collect and store"
msgstr "ما نقوم بجمعه وتخزينه"

#: plugin-fw/includes/privacy/class-yith-privacy.php:93
msgctxt "Privacy Policy Content"
msgid "This sample language includes the basics around what personal data your store may be collecting, storing and sharing, as well as who may have access to that data. Depending on what settings are enabled and which additional plugins are used, the specific information shared by your store will vary. We recommend consulting with a lawyer when deciding what information to disclose on your privacy policy."
msgstr "يشتمل نموذج اللغة هذا على أساسيات حول البيانات الشخصية التي قد يقوم متجرك بتجميعها وتخزينها ومشاركتها، فضلاً عن الأشخاص الذين يمكنهم الوصول إلى تلك البيانات. استنادًا إلى الإعدادات التي تم تمكينها والإضافات الملحقة المستخدمة، ستختلف المعلومات المحددة التي يشاركها متجرك. نوصي بالتشاور مع أحد المحامين عند تحديد المعلومات التي يجب الإفصاح عنها في سياسة الخصوصية الخاصة بك."

#: plugin-fw/includes/privacy/class-yith-privacy.php:61
msgctxt "Privacy Policy Guide Title"
msgid "YITH Plugins"
msgstr "إضافات YITH"

#: includes/class-yith-wcwl-privacy.php:276
msgid "Items added"
msgstr "العناصر المضافة"

#: includes/class-yith-wcwl-privacy.php:273
msgid "Title"
msgstr "عنوان"

#: includes/class-yith-wcwl-privacy.php:146 plugin-options/lists-options.php:33
#: plugin-options/lists-options.php:39
msgid "Wishlists"
msgstr "قوائم الرغبات"

#: includes/class-yith-wcwl-privacy.php:56
msgid "We’ll also use cookies to keep track of wishlist contents while you’re browsing our site."
msgstr "سنستخدم أيضًا ملفات تعريف الارتباط لتتبع محتويات قائمة الرغبات أثناء تصفحك لموقعنا."

#: includes/class-yith-wcwl-privacy.php:54
msgid "Wishlists you’ve created: we’ll keep track of the wishlists you create, and make them visible to the store staff"
msgstr "قوائم الرغبات التي أنشأتها: سيتم تتبعها وجعلها مرئية لموظفي المتجر"

#: includes/class-yith-wcwl-privacy.php:53
msgid "Products you’ve added to the wishlist: we’ll use this to show you and other users your favourite products, and to create targeted email campaigns."
msgstr "المنتجات التي أضفتها إلى قائمة الرغبات: سنستخدمها لعرض منتجاتك المفضلة وللمستخدمين الآخرين ولإنشاء حملات بريد إلكتروني مستهدفة."

#. Translators: %s Order number.
#: includes/class-yith-wcwl-privacy.php:232
msgid "Removed wishlist %s."
msgstr "إزالة قائمة الرغبات %s."

#. Translators: %s Order number.
#: includes/class-yith-wcwl-privacy.php:236
msgid "Wishlist %s has been retained."
msgstr "تمّ حفظ قائمة الرغبات %s."

#: includes/class-yith-wcwl-privacy.php:274
msgctxt "date when wishlist was created"
msgid "Created on"
msgstr "تم الإنشاء في"

#: includes/class-yith-wcwl-privacy.php:275
msgid "Visibility"
msgstr "الظهور"

#: includes/class-yith-wcwl-privacy.php:51
msgid "While you visit our site, we’ll track:"
msgstr "أثناء زيارتك لموقعنا، سنتتبع:"

#: includes/class-yith-wcwl-privacy.php:93
#: includes/class-yith-wcwl-privacy.php:109
msgid "Customer wishlists"
msgstr "قوائم رغبات العميل"

#: includes/class-yith-wcwl-privacy.php:271
msgid "Token"
msgstr "رمز Token"

#: includes/class-yith-wcwl-privacy.php:63
msgid "Our team members have access to this information to offer you better deals for the products you love."
msgstr "يمكن لأعضاء فريقنا الوصول إلى هذه المعلومات لنقدّم لك أفضل العروض للمنتجات التي تحبها."

#: includes/class-yith-wcwl-privacy.php:61
msgid "Wishlist details, such as products added, date of addition, name and privacy settings of your wishlists"
msgstr "تفاصيل قائمة الرغبات، مثل المنتجات المضافة وتاريخ الإضافة، اسم وإعدادات الخصوصية لقوائم رغباتك المفضلة"

#: includes/class-yith-wcwl-privacy.php:59
msgid "Members of our team have access to the information you provide us with. For example, both Administrators and Shop Managers can access:"
msgstr "يستطيع أعضاء فريقنا الوصول إلى المعلومات التي تزوّدنا بها. على سبيل المثال، يمكن لكل من المشرفين ومديري المتاجر الوصول إلى:"

#: plugin-fw/templates/fields/icons.php:78
msgid "Set Default"
msgstr "تعيين كافتراضي"

#: plugin-fw/includes/class-yit-plugin-panel.php:559
#: plugin-fw/includes/class-yit-plugin-panel.php:562
msgid "How to install premium version"
msgstr "كيفية تنصيب نسخة بريميوم"

#: plugin-fw/templates/fields/upload.php:39
msgid "Reset"
msgstr "استعادة"

#: includes/functions-yith-wcwl.php:602
msgid "None"
msgstr "لا يوجد"

#: plugin-fw/templates/bh-onboarding/onboarding-tabs.php:60
msgid "Save"
msgstr "حفظ"

#: plugin-fw/templates/fields/image-gallery.php:26
#: plugin-fw/templates/fields/image-gallery.php:37
msgid "Delete"
msgstr "حذف"

#: plugin-fw/templates/fields/customtabs.php:25
#: plugin-fw/templates/fields/customtabs.php:68 templates/wishlist-view.php:607
msgid "Remove"
msgstr "إزالة"

#: includes/functions-yith-wcwl.php:721
msgid "Public"
msgstr "عام"

#: includes/functions-yith-wcwl.php:716
msgid "Private"
msgstr "خاص"

#: templates/wishlist-view-mobile.php:232 templates/wishlist-view.php:544
msgid "Move"
msgstr "نقل"

#: includes/class-yith-wcwl-wishlist-item.php:201
msgid "Free!"
msgstr "مجانا!"

#: templates/wishlist-view-mobile.php:283 templates/wishlist-view.php:250
#: templates/wishlist-view.php:607
msgid "Remove this product"
msgstr "إزالة هذا المنتج"

#: templates/wishlist-view.php:164
msgid "Stock status"
msgstr "حالة المخزون"

#: templates/wishlist-view.php:122
msgid "Unit price"
msgstr "سعر الوحدة"

#: templates/wishlist-view-header.php:86
msgid "Edit title"
msgstr "تحرير العنوان"

#: plugin-fw/templates/panel/v2/panel-content-page.php:58
#: plugin-fw/templates/panel/v2/woocommerce/woocommerce-form.php:70
#: plugin-fw/templates/panel/woocommerce/woocommerce-form.php:41
msgid "Reset Defaults"
msgstr "إعادة تعيين الإعدادات الافتراضية"

#: plugin-fw/templates/fields/sidebars.php:54
msgid "Right Sidebar"
msgstr "الشريط الجانبي الأيمن"

#: plugin-fw/templates/fields/sidebars.php:41
#: plugin-fw/templates/fields/sidebars.php:56
msgid "Choose a sidebar"
msgstr "اختيار الشريط الجانبي"

#: plugin-fw/templates/fields/sidebars.php:23
msgid "Right sidebar"
msgstr "الشريط الجانبي الأيمن"

#: plugin-fw/templates/fields/image-gallery.php:34
msgid "Add to gallery"
msgstr "إضافة إلى المعرض"

#: plugin-fw/templates/fields/image-gallery.php:33
msgid "Add Images to Gallery"
msgstr "إضافة الصور إلى المعرض"

#: plugin-fw/templates/fields/image-gallery.php:36
msgid "Delete image"
msgstr "حذف الصورة"

#: plugin-fw/templates/fields/customtabs.php:93
msgid "Do you want to remove the custom tab?"
msgstr "هل تريد إزالة علامة التبويب المخصصة؟"

#: plugin-fw/templates/fields/customtabs.php:52
msgid "Add custom product tab"
msgstr "إضافة علامة تبويب المنتج المخصصة"

#: plugin-fw/templates/fields/customtabs.php:41
#: plugin-fw/templates/fields/customtabs.php:82
msgid "Content of the tab. (HTML is supported)"
msgstr "محتوى علامة التبويب. (يدعم HTML)"

#: plugin-fw/templates/fields/customtabs.php:17
msgid "Expand all"
msgstr "توسيع الكل"

#: plugin-fw/templates/fields/customtabs.php:17
msgid "Close all"
msgstr "إغلاق الكل"

#: plugin-fw/templates/fields/upload.php:34
#: plugin-fw/templates/panel/woocommerce/woocommerce-upload.php:37
msgid "Upload"
msgstr "رفع"

#: templates/share.php:102 templates/share.php:103
msgid "Email"
msgstr "البريد الإلكتروني"

#: plugin-fw/includes/class-yit-plugin-panel.php:1563
msgid "Database imported correctly."
msgstr "تم استيراد قاعدة البيانات بشكل صحيح."

#: plugin-fw/includes/class-yit-plugin-panel.php:1561
msgid "Element updated correctly."
msgstr "تم تحديث العناصر بشكل صحيح."

#: plugin-fw/includes/class-yit-plugin-panel.php:1560
msgid "Element deleted correctly."
msgstr "تم حذف العناصر بشكل صحيح."

#: plugin-fw/includes/class-yit-plugin-panel.php:1564
msgid "An error has occurred during import. Please try again."
msgstr "حدث خطأ أثناء الاستيراد. حاول مرة أخرى."

#: plugin-fw/includes/class-yit-plugin-panel.php:1565
msgid "The added file is not valid."
msgstr "الملف الذي تم إضافته غير صالح."

#: plugin-fw/includes/class-yit-plugin-panel.php:1566
msgid "Sorry, import is disabled."
msgstr "عذرًا، تمّ تعطيل الاستيراد."

#: plugin-fw/includes/class-yit-plugin-panel.php:1567
msgid "Sorting successful."
msgstr "تم الفرز بنجاح."

#: plugin-fw/includes/class-yit-pointers.php:80
msgid "Plugins Activated"
msgstr "تم تنشيط الإضافات"

#: plugin-fw/templates/fields/customtabs.php:40
#: plugin-fw/templates/fields/customtabs.php:81
msgid "Value"
msgstr "القيمة"

#: plugin-fw/templates/fields/image-gallery.php:35
msgid "Add images"
msgstr "إضافة صور"

#: plugin-fw/templates/fields/sidebars.php:26
#: plugin-fw/templates/fields/sidebars.php:29
msgid "No sidebar"
msgstr "بدون شريط جانبي"

#: templates/share.php:72 templates/share.php:73
msgid "Pinterest"
msgstr "بنترست"

#: includes/functions-yith-wcwl.php:711
msgid "Shared"
msgstr "مُشتَرك"

#: plugin-fw/includes/class-yit-pointers.php:96
msgid "Plugins Upgraded"
msgstr "ترقية الإضافات"

#: templates/wishlist-view-mobile.php:199 templates/wishlist-view.php:431
msgid "In Stock"
msgstr "متوفر في المخزون"

#: plugin-fw/templates/fields/sidebars.php:20
msgid "Left sidebar"
msgstr "الشريط الجانبي (يسار)"

#: plugin-fw/templates/fields/sidebars.php:39
msgid "Left Sidebar"
msgstr "الشريط الجانبي (يسار)"

#. translators: 1. Date product was added to wishlist.
#: templates/wishlist-view.php:479
msgid "Added on: %s"
msgstr "تمت الإضافة في: %s"

#: templates/wishlist-view-mobile.php:199 templates/wishlist-view.php:431
msgid "Out of stock"
msgstr "غير متوفر في المخزون"

#: templates/wishlist-view.php:102
msgid "Product name"
msgstr "اسم المنتج"

#. Plugin URI of the plugin
#: init.php
msgid "https://yithemes.com/themes/plugins/yith-woocommerce-wishlist/"
msgstr "https://yithemes.com/themes/plugins/yith-woocommerce-wishlist/"

#. Author URI of the plugin
#: init.php
msgid "https://yithemes.com/"
msgstr "https://yithemes.com/"

#: plugin-fw/includes/class-yit-plugin-subpanel.php:127
#: plugin-fw/templates/panel/panel-content-page.php:29
msgid "Save Changes"
msgstr "حفظ التغييرات"

#: plugin-fw/includes/class-yit-plugin-panel.php:89
msgid "Plugin Settings"
msgstr "اعدادات الاضافة"

#: plugin-fw/includes/class-yit-plugin-panel.php:90
msgid "Settings"
msgstr "الإعدادات"

#: templates/share.php:56 templates/share.php:57
msgid "Facebook"
msgstr "فيسبوك"

#: plugin-fw/templates/fields/customtabs.php:34
#: plugin-fw/templates/fields/customtabs.php:76
msgid "Name"
msgstr "الاسم"

#: plugin-fw/includes/class-yit-assets.php:261
#: plugin-fw/includes/class-yit-plugin-subpanel.php:134
#: plugin-fw/templates/panel/panel-content-page.php:14
msgid "Are you sure?"
msgstr "هل أنت متأكد؟"

#: plugin-fw/includes/class-yit-plugin-panel-woocommerce.php:607
msgid "The changes you have made will be lost if you leave this page."
msgstr "سيتم فقد التغييرات التي أجريتها إذا تركت هذه الصفحة."

#: plugin-fw/includes/class-yit-assets.php:262
#: plugin-fw/includes/class-yit-plugin-subpanel.php:134
#: plugin-fw/templates/panel/panel-content-page.php:14
msgid "If you continue with this action, you will reset all options in this page."
msgstr "إذا تابعت هذا الإجراء، فسيتم إعادة تعيين جميع الخيارات في هذه الصفحة."

#: plugin-fw/includes/class-yit-plugin-subpanel.php:137
#: plugin-fw/templates/panel/panel-content-page.php:38
msgid "Reset to default"
msgstr "إعادة تعيين إلى الافتراضي"

#: plugin-fw/includes/class-yit-plugin-panel.php:1557
msgid "The element you have entered already exists. Please, enter another name."
msgstr "العنصر الذي أدخلته موجود بالفعل. من فضلك، أدخل اسم آخر."

#: plugin-fw/includes/class-yit-plugin-panel.php:1558
msgid "Settings saved"
msgstr "تم حفظ الإعدادات"

#: plugin-fw/includes/class-yit-plugin-panel.php:1559
msgid "Settings reset"
msgstr "إعادة تعيين الإعدادات"

#: includes/class-yith-wcwl-install.php:135
msgid "Wishlist"
msgstr "قائمة الرغبات"

#: plugin-options/customization/labels-options.php:71
#: plugin-options/customization/style-options.php:243
#: plugin-options/settings/wishlist_page-options.php:28
#: plugin-options/settings/wishlist_page-options.php:35
msgid "Wishlist page"
msgstr "صفحة قائمة الرغبات"

#. Plugin Name of the plugin
#: init.php
msgid "YITH WooCommerce Wishlist"
msgstr "إضافة قائمة الرغبات ‫YITH WooCommerce Wishlist"

#: plugin-options/customization/labels-options.php:76
msgid "Default wishlist name"
msgstr "اسم قائمة الرغبات الافتراضي"

#. translators: 1. Blog name.
#: plugin-options/customization/labels-options.php:88
msgid "My wishlist on %s"
msgstr "قائمة رغباتي على %s"

#: includes/class-yith-wcwl-ajax-handler.php:201
#: includes/class-yith-wcwl-ajax-handler.php:250
msgid "Product successfully removed."
msgstr "تمت إزالة المنتج بنجاح."

#: includes/class-yith-wcwl-shortcode.php:474
msgid "Share on:"
msgstr "مشاركة على:"

#: plugin-options/settings/add_to_wishlist-options.php:111
msgid "After \"Add to cart\""
msgstr "بعد \"إضافة إلى السلة\""

#: plugin-options/settings/add_to_wishlist-options.php:112
msgid "After thumbnails"
msgstr "بعد الصور المصغرة"

#: plugin-options/settings/add_to_wishlist-options.php:113
msgid "After summary"
msgstr "بعد الملخص"

#: plugin-options/settings/add_to_wishlist-options.php:82
#: plugin-options/settings/add_to_wishlist-options.php:114
msgid "Use shortcode"
msgstr "استخدام الكود القصير shortcode"

#: plugin-options/settings/wishlist_page-options.php:119
msgid "Redirect to cart"
msgstr "إعادة التوجيه إلى السلة"

#: plugin-options/customization/labels-options.php:25
msgid "\"Add to wishlist\" text"
msgstr "نص \"إضافة إلى قائمة الرغبات\""

#: includes/class-yith-wcwl-add-to-wishlist-button.php:866
#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:118
#: plugin-options/customization/labels-options.php:28
#: plugin-options/customization/style-options.php:25
msgid "Add to wishlist"
msgstr "إضافة إلى قائمة الرغبات"

#: plugin-options/customization/labels-options.php:49
msgid "\"Browse wishlist\" text"
msgstr "نص \"تصفُّح قائمة الرغبات\""

#: plugin-options/settings/wishlist_page-options.php:129
msgid "Remove the product from the wishlist after it has been added to the cart"
msgstr "إزالة المنتج من قائمة الرغبات بعد إضافته إلى سلة المشتريات"

#: plugin-options/customization/labels-options.php:57
msgid "\"Product already in wishlist\" text"
msgstr "نص \"المنتج موجود بالفعل في قائمة الرغبات\""

#: includes/class-yith-wcwl-add-to-wishlist-button.php:882
#: plugin-options/customization/labels-options.php:60
msgid "The product is already in your wishlist!"
msgstr "المنتج موجود بالفعل في قائمة رغباتك!"

#: plugin-options/customization/labels-options.php:33
msgid "\"Product added\" text"
msgstr "نص \"تمت إضافة المنتج\""

#: includes/class-yith-wcwl-add-to-wishlist-button.php:870
#: plugin-options/customization/labels-options.php:36
msgid "Product added!"
msgstr "تمت إضافة المنتج!"

#: plugin-options/customization/labels-options.php:102
msgid "\"Add to cart\" text"
msgstr "نص \"إضافة إلى السلة\""

#: includes/class-yith-wcwl-frontend.php:1011
#: includes/class-yith-wcwl-shortcode.php:270
#: plugin-options/customization/labels-options.php:105
#: templates/wishlist-view-footer-mobile.php:59
#: templates/wishlist-view-footer.php:59
msgid "Add to cart"
msgstr "إضافة إلى السلة"

#: plugin-options/customization/style-options.php:228
msgid "Custom CSS"
msgstr "CSS مخصص"

#: plugin-options/customization/style-options.php:169
#: plugin-options/customization/style-options.php:183
msgid "\"Added to wishlist\" icon"
msgstr "أيقونة \"تمت الإضافة إلى قائمة الرغبات\""

#: plugin-options/customization/style-options.php:326
msgid "\"Add to cart\" icon"
msgstr "أيقونة \"إضافة إلى السلة\""

#: includes/class-yith-wcwl-shortcode.php:516
#: plugin-options/settings/wishlist_page-options.php:147
msgid "Share on Facebook"
msgstr "مشاركة على Facebook"

#: includes/class-yith-wcwl-shortcode.php:527
#: plugin-options/settings/wishlist_page-options.php:165
msgid "Pin on Pinterest"
msgstr "مشاركة على Pinterest"

#: plugin-options/settings/wishlist_page-options.php:174
msgid "Share by email"
msgstr "مشاركة عبر البريد الإلكتروني"

#: plugin-options/customization/style-options.php:107
#: plugin-options/customization/style-options.php:120
msgid "\"Add to wishlist\" icon"
msgstr "أيقونة \"إضافة إلى قائمة الرغبات\""

#: plugin-options/settings/general-options.php:59
msgid "Enable slider in wishlist"
msgstr "تفعيل سلايدر في قائمة الرغبات"

#: plugin-options/customization/style-options.php:52
#: plugin-options/customization/style-options.php:270
#: plugin-options/customization/style-options.php:372
#: plugin-options/customization/style-options.php:421
#: plugin-options/customization/style-options.php:469
#: plugin-options/customization/style-options.php:558
#: plugin-options/customization/style-options.php:605
#: plugin-options/customization/style-options.php:660
#: plugin-options/customization/style-options.php:707
#: plugin-options/customization/style-options.php:754
msgid "Background"
msgstr "الخلفية"

#: plugin-options/customization/style-options.php:57
#: plugin-options/customization/style-options.php:275
#: plugin-options/customization/style-options.php:377
#: plugin-options/customization/style-options.php:426
#: plugin-options/customization/style-options.php:474
#: plugin-options/customization/style-options.php:511
msgid "Text"
msgstr "النص"

#: plugin-options/customization/style-options.php:62
#: plugin-options/customization/style-options.php:280
#: plugin-options/customization/style-options.php:382
#: plugin-options/customization/style-options.php:431
#: plugin-options/customization/style-options.php:479
msgid "Border"
msgstr "الإطار"

#: plugin-options/customization/style-options.php:563
#: plugin-options/customization/style-options.php:610
#: plugin-options/customization/style-options.php:665
#: plugin-options/customization/style-options.php:712
#: plugin-options/customization/style-options.php:759
msgid "Background hover"
msgstr "خلفية مؤثر تمرير الفأرة"

#: plugin-options/customization/style-options.php:69
#: plugin-options/customization/style-options.php:288
#: plugin-options/customization/style-options.php:390
#: plugin-options/customization/style-options.php:439
msgid "Background Hover"
msgstr "خلفية مؤثر تمرير الفأرة"

#: includes/class-yith-wcwl-install.php:133
msgctxt "page_slug"
msgid "wishlist"
msgstr "wishlist"

#: includes/class-yith-wcwl-wishlists.php:281
#: includes/class-yith-wcwl-wishlists.php:287
msgid "An error occurred while adding the products to the wishlist."
msgstr "حدث خطأ أثناء إضافة المنتجات إلى قائمة الرغبات."

#: includes/class-yith-wcwl-wishlists.php:387
#: includes/class-yith-wcwl-wishlists.php:413
msgid "Error. Unable to remove the product from the wishlist."
msgstr "خطأ. تعذّر إزالة المنتج من قائمة الرغبات."

#: plugin-fw/includes/class-yith-system-status.php:217
msgid "YITH Plugins"
msgstr "إضافات YITH"

#: plugin-options/customization/style-options.php:516
msgid "Text hover"
msgstr "مؤثر تمرير الفأرة للنص"

#: plugin-options/customization/labels-options.php:93
msgid "Social text"
msgstr "نص المشاركة الاجتماعية"

#: plugin-options/settings/general-options.php:53
msgid "YITH WooCommerce Frequently Bought Together Integration"
msgstr "دمج وتكامل YITH WooCommerce Frequently Bought Together"

#: includes/widgets/elementor/class-yith-wcwl-elementor-add-to-wishlist.php:128
#: plugin-options/customization/labels-options.php:52
msgid "Browse wishlist"
msgstr "تصفُّح قائمة الرغبات"

#: templates/wishlist-view-mobile.php:304 templates/wishlist-view.php:649
msgid "No products added to the wishlist"
msgstr "لم يتم إضافة أي منتجات إلى قائمة الرغبات"