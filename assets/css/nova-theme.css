/* 
 * Nova Yemen - Modern Theme CSS
 * نوفا يمن - ملف التصميم الحديث
 * تحسينات الأداء والتصميم المريح للعين
 */

:root {
  /* نظام ألوان مريح للعين */
  --primary-color: #4a90e2;
  --primary-dark: #357abd;
  --secondary-color: #7b68ee;
  --accent-color: #50c878;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --error-color: #dc3545;

  /* ألوان الخلفية الهادئة */
  --bg-primary: #1a1d23;
  --bg-secondary: #242831;
  --bg-tertiary: #2d3748;
  --bg-card: rgba(36, 40, 49, 0.95);
  --bg-glass: rgba(45, 55, 72, 0.9);

  /* ألوان النص المريحة */
  --text-primary: #f7fafc;
  --text-secondary: #cbd5e0;
  --text-muted: #a0aec0;

  /* الظلال والحدود الناعمة */
  --shadow-light: 0 4px 20px rgba(74, 144, 226, 0.08);
  --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.25);
  --shadow-heavy: 0 12px 40px rgba(0, 0, 0, 0.35);
  --border-glass: 1px solid rgba(74, 144, 226, 0.15);

  /* المسافات */
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* نصف الأقطار */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* الانتقالات */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.6s ease;

  /* أحجام الأزرار */
  --btn-size-sm: 10px 16px;
  --btn-size-md: 12px 24px;
  --btn-size-lg: 16px 32px;
  --btn-size-xl: 20px 40px;

  /* أحجام الخطوط للأزرار */
  --btn-font-sm: 0.875rem;
  --btn-font-md: 1rem;
  --btn-font-lg: 1.125rem;
  --btn-font-xl: 1.25rem;

  /* ارتفاعات الأزرار */
  --btn-height-sm: 36px;
  --btn-height-md: 44px;
  --btn-height-lg: 52px;
  --btn-height-xl: 60px;

  /* نظام Typography */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* أوزان الخطوط */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* ارتفاعات الأسطر */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* نقاط التوقف المحسنة */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* أحجام الحاويات */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* إعادة تعيين أساسية */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* تحسينات الخطوط */
@font-face {
  font-family: 'Cairo-Fallback';
  src: local('Tahoma'), local('Arial');
  font-display: swap;
}

/* الجسم الأساسي */
body {
  font-family: "Cairo", "Cairo-Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  font-display: swap;

  /* تحسينات الأداء */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
  contain: layout style;
}

/* Header محسن */
.nova-header {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(26, 29, 35, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 15px 0;
  z-index: 1000;
  box-shadow: var(--shadow-medium);
  transition: var(--transition-normal);
  border-bottom: 1px solid rgba(74, 144, 226, 0.1);
}

.nova-header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--spacing-lg);
}

.nova-logo {
  font-size: 2rem;
  font-weight: 900;
  color: var(--primary-color);
  text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
  text-decoration: none;
  transition: var(--transition-normal);
}

.nova-logo:hover {
  color: #6afcff;
  transform: scale(1.05);
}

/* Navigation */
.nova-nav {
  display: flex;
  gap: var(--spacing-xl);
}

.nova-nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  font-weight: 500;
  position: relative;
  border: 1px solid transparent;
}

.nova-nav-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transition: var(--transition-normal);
  transform: translateX(-50%);
}

.nova-nav-link:hover {
  color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
  transform: translateY(-2px);
  border-color: rgba(74, 144, 226, 0.2);
}

.nova-nav-link:hover::before,
.nova-nav-link.active::before {
  width: 100%;
}

.nova-nav-link.active {
  color: var(--primary-color);
  background: rgba(74, 144, 226, 0.15);
  border-color: rgba(74, 144, 226, 0.3);
}

/* Mobile Menu Button */
.nova-mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  z-index: 1001;
  position: relative;
}

.nova-mobile-menu-btn:hover {
  background: rgba(74, 144, 226, 0.1);
  color: #6afcff;
  transform: scale(1.1);
}

.nova-mobile-menu-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.nova-mobile-menu-btn.active i {
  transform: rotate(90deg);
}

/* Mobile Navigation */
.nova-nav.mobile-active {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: var(--bg-card);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 2px solid rgba(74, 144, 226, 0.2);
  box-shadow: var(--shadow-heavy);
  z-index: 1000;
  display: flex !important;
  flex-direction: column;
  padding: var(--spacing-lg);
  gap: var(--spacing-md);
  max-height: calc(100vh - 80px);
  overflow-y: auto;
}

.nova-nav.mobile-active .nova-nav-link {
  display: block;
  width: 100%;
  padding: var(--spacing-md);
  text-align: center;
  border-radius: var(--radius-md);
  border: 1px solid rgba(74, 144, 226, 0.2);
  background: rgba(255, 255, 255, 0.05);
  margin-bottom: var(--spacing-sm);
}

.nova-nav.mobile-active .nova-nav-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

/* Container */
.nova-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  margin-top: 100px; /* مساحة للـ fixed header */
}

/* Cards */
.nova-card {
  background: var(--bg-card);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  border: 2px solid rgba(74, 144, 226, 0.2);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nova-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(74, 144, 226, 0.1), 
    transparent
  );
  transition: var(--transition-slow);
}

.nova-card:hover::before {
  left: 100%;
}

.nova-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
  border-color: rgba(74, 144, 226, 0.4);
  background: linear-gradient(135deg, 
    rgba(74, 144, 226, 0.1), 
    var(--bg-card)
  );
}

/* نظام الأزرار المحسن */
.nova-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--btn-size-md);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  font-size: var(--btn-font-md);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-height: var(--btn-height-md);
  text-align: center;
  font-family: inherit;
  white-space: nowrap;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
}

/* تأثير الإضاءة */
.nova-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.25),
    transparent
  );
  transition: var(--transition-slow);
  z-index: 1;
}

.nova-btn:hover::before {
  left: 100%;
}

/* تأثير الضغط */
.nova-btn:active {
  transform: translateY(1px) scale(0.98);
}

/* تأثير التركيز */
.nova-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
}

/* أحجام الأزرار */
.nova-btn-sm {
  padding: var(--btn-size-sm);
  font-size: var(--btn-font-sm);
  min-height: var(--btn-height-sm);
  border-radius: var(--radius-md);
}

.nova-btn-lg {
  padding: var(--btn-size-lg);
  font-size: var(--btn-font-lg);
  min-height: var(--btn-height-lg);
  border-radius: var(--radius-xl);
}

.nova-btn-xl {
  padding: var(--btn-size-xl);
  font-size: var(--btn-font-xl);
  min-height: var(--btn-height-xl);
  border-radius: var(--radius-xl);
}

/* أنواع الأزرار */
.nova-btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-light);
}

.nova-btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-dark);
}

.nova-btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.nova-btn-secondary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.nova-btn-success {
  background: linear-gradient(135deg, var(--success-color), #218838);
  color: white;
  border-color: var(--success-color);
  box-shadow: var(--shadow-light);
}

.nova-btn-success:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.nova-btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #e0a800);
  color: #1a1d23;
  border-color: var(--warning-color);
  box-shadow: var(--shadow-light);
}

.nova-btn-warning:hover {
  background: linear-gradient(135deg, #e0a800, #d39e00);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.nova-btn-danger {
  background: linear-gradient(135deg, var(--error-color), #c82333);
  color: white;
  border-color: var(--error-color);
  box-shadow: var(--shadow-light);
}

.nova-btn-danger:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* أزرار الخطوط */
.nova-btn-outline-primary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.nova-btn-outline-primary:hover {
  background: var(--primary-color);
  color: white;
}

.nova-btn-outline-success {
  background: transparent;
  color: var(--success-color);
  border-color: var(--success-color);
}

.nova-btn-outline-success:hover {
  background: var(--success-color);
  color: white;
}

/* أزرار شبحية */
.nova-btn-ghost {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nova-btn-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* أزرار مع أيقونات */
.nova-btn-icon-only {
  min-width: var(--btn-height-md);
  padding: var(--spacing-md);
  aspect-ratio: 1;
}

.nova-btn-icon-only.nova-btn-sm {
  min-width: var(--btn-height-sm);
  padding: var(--spacing-sm);
}

.nova-btn-icon-only.nova-btn-lg {
  min-width: var(--btn-height-lg);
  padding: var(--spacing-lg);
}

/* حالات خاصة */
.nova-btn:disabled,
.nova-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  pointer-events: none;
}

.nova-btn-loading {
  position: relative;
  color: transparent !important;
}

.nova-btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: nova-spin 1s linear infinite;
}

@keyframes nova-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* نظام Grid المحسن */
.nova-grid {
  display: grid;
  gap: var(--spacing-xl);
}

/* Grid بأعمدة ثابتة */
.nova-grid-1 { grid-template-columns: 1fr; }
.nova-grid-2 { grid-template-columns: repeat(2, 1fr); }
.nova-grid-3 { grid-template-columns: repeat(3, 1fr); }
.nova-grid-4 { grid-template-columns: repeat(4, 1fr); }
.nova-grid-5 { grid-template-columns: repeat(5, 1fr); }
.nova-grid-6 { grid-template-columns: repeat(6, 1fr); }

/* Grid متجاوب */
.nova-grid-auto-sm { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
.nova-grid-auto-md { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.nova-grid-auto-lg { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }
.nova-grid-auto-xl { grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)); }

/* Grid gaps */
.nova-grid-gap-sm { gap: var(--spacing-sm); }
.nova-grid-gap-md { gap: var(--spacing-md); }
.nova-grid-gap-lg { gap: var(--spacing-lg); }
.nova-grid-gap-xl { gap: var(--spacing-xl); }
.nova-grid-gap-2xl { gap: var(--spacing-2xl); }

/* Flexbox utilities */
.nova-flex { display: flex; }
.nova-flex-col { flex-direction: column; }
.nova-flex-row { flex-direction: row; }
.nova-flex-wrap { flex-wrap: wrap; }
.nova-flex-nowrap { flex-wrap: nowrap; }

.nova-justify-start { justify-content: flex-start; }
.nova-justify-center { justify-content: center; }
.nova-justify-end { justify-content: flex-end; }
.nova-justify-between { justify-content: space-between; }
.nova-justify-around { justify-content: space-around; }
.nova-justify-evenly { justify-content: space-evenly; }

.nova-items-start { align-items: flex-start; }
.nova-items-center { align-items: center; }
.nova-items-end { align-items: flex-end; }
.nova-items-stretch { align-items: stretch; }

.nova-self-start { align-self: flex-start; }
.nova-self-center { align-self: center; }
.nova-self-end { align-self: flex-end; }
.nova-self-stretch { align-self: stretch; }

/* Gap utilities */
.nova-gap-xs { gap: var(--spacing-xs); }
.nova-gap-sm { gap: var(--spacing-sm); }
.nova-gap-md { gap: var(--spacing-md); }
.nova-gap-lg { gap: var(--spacing-lg); }
.nova-gap-xl { gap: var(--spacing-xl); }
.nova-gap-2xl { gap: var(--spacing-2xl); }

/* Form Elements */
.nova-form-group {
  margin-bottom: var(--spacing-lg);
}

.nova-form-label {
  display: block;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  font-size: 1rem;
}

.nova-form-input,
.nova-form-select,
.nova-form-textarea {
  width: 100%;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(74, 144, 226, 0.2);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition-normal);
  font-family: inherit;
}

.nova-form-input:focus,
.nova-form-select:focus,
.nova-form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.nova-form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* نظام Typography */
.nova-text-xs { font-size: var(--font-size-xs); }
.nova-text-sm { font-size: var(--font-size-sm); }
.nova-text-base { font-size: var(--font-size-base); }
.nova-text-lg { font-size: var(--font-size-lg); }
.nova-text-xl { font-size: var(--font-size-xl); }
.nova-text-2xl { font-size: var(--font-size-2xl); }
.nova-text-3xl { font-size: var(--font-size-3xl); }
.nova-text-4xl { font-size: var(--font-size-4xl); }
.nova-text-5xl { font-size: var(--font-size-5xl); }
.nova-text-6xl { font-size: var(--font-size-6xl); }

/* أوزان الخطوط */
.nova-font-light { font-weight: var(--font-weight-light); }
.nova-font-normal { font-weight: var(--font-weight-normal); }
.nova-font-medium { font-weight: var(--font-weight-medium); }
.nova-font-semibold { font-weight: var(--font-weight-semibold); }
.nova-font-bold { font-weight: var(--font-weight-bold); }
.nova-font-extrabold { font-weight: var(--font-weight-extrabold); }
.nova-font-black { font-weight: var(--font-weight-black); }

/* ارتفاعات الأسطر */
.nova-leading-tight { line-height: var(--line-height-tight); }
.nova-leading-snug { line-height: var(--line-height-snug); }
.nova-leading-normal { line-height: var(--line-height-normal); }
.nova-leading-relaxed { line-height: var(--line-height-relaxed); }
.nova-leading-loose { line-height: var(--line-height-loose); }

/* محاذاة النص */
.nova-text-left { text-align: left; }
.nova-text-center { text-align: center; }
.nova-text-right { text-align: right; }
.nova-text-justify { text-align: justify; }

/* ألوان النص */
.nova-text-primary { color: var(--text-primary); }
.nova-text-secondary { color: var(--text-secondary); }
.nova-text-muted { color: var(--text-muted); }
.nova-text-accent { color: var(--primary-color); }
.nova-text-success { color: var(--success-color); }
.nova-text-warning { color: var(--warning-color); }
.nova-text-danger { color: var(--error-color); }

/* تأثيرات النص */
.nova-text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nova-text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nova-text-glow {
  text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
}

/* Spacing Utilities */
.nova-m-0 { margin: 0; }
.nova-m-xs { margin: var(--spacing-xs); }
.nova-m-sm { margin: var(--spacing-sm); }
.nova-m-md { margin: var(--spacing-md); }
.nova-m-lg { margin: var(--spacing-lg); }
.nova-m-xl { margin: var(--spacing-xl); }
.nova-m-2xl { margin: var(--spacing-2xl); }

.nova-mt-0 { margin-top: 0; }
.nova-mt-xs { margin-top: var(--spacing-xs); }
.nova-mt-sm { margin-top: var(--spacing-sm); }
.nova-mt-md { margin-top: var(--spacing-md); }
.nova-mt-lg { margin-top: var(--spacing-lg); }
.nova-mt-xl { margin-top: var(--spacing-xl); }
.nova-mt-2xl { margin-top: var(--spacing-2xl); }

.nova-mb-0 { margin-bottom: 0; }
.nova-mb-xs { margin-bottom: var(--spacing-xs); }
.nova-mb-sm { margin-bottom: var(--spacing-sm); }
.nova-mb-md { margin-bottom: var(--spacing-md); }
.nova-mb-lg { margin-bottom: var(--spacing-lg); }
.nova-mb-xl { margin-bottom: var(--spacing-xl); }
.nova-mb-2xl { margin-bottom: var(--spacing-2xl); }

.nova-ml-0 { margin-left: 0; }
.nova-ml-xs { margin-left: var(--spacing-xs); }
.nova-ml-sm { margin-left: var(--spacing-sm); }
.nova-ml-md { margin-left: var(--spacing-md); }
.nova-ml-lg { margin-left: var(--spacing-lg); }
.nova-ml-xl { margin-left: var(--spacing-xl); }
.nova-ml-2xl { margin-left: var(--spacing-2xl); }

.nova-mr-0 { margin-right: 0; }
.nova-mr-xs { margin-right: var(--spacing-xs); }
.nova-mr-sm { margin-right: var(--spacing-sm); }
.nova-mr-md { margin-right: var(--spacing-md); }
.nova-mr-lg { margin-right: var(--spacing-lg); }
.nova-mr-xl { margin-right: var(--spacing-xl); }
.nova-mr-2xl { margin-right: var(--spacing-2xl); }

.nova-p-0 { padding: 0; }
.nova-p-xs { padding: var(--spacing-xs); }
.nova-p-sm { padding: var(--spacing-sm); }
.nova-p-md { padding: var(--spacing-md); }
.nova-p-lg { padding: var(--spacing-lg); }
.nova-p-xl { padding: var(--spacing-xl); }
.nova-p-2xl { padding: var(--spacing-2xl); }

.nova-pt-0 { padding-top: 0; }
.nova-pt-xs { padding-top: var(--spacing-xs); }
.nova-pt-sm { padding-top: var(--spacing-sm); }
.nova-pt-md { padding-top: var(--spacing-md); }
.nova-pt-lg { padding-top: var(--spacing-lg); }
.nova-pt-xl { padding-top: var(--spacing-xl); }
.nova-pt-2xl { padding-top: var(--spacing-2xl); }

.nova-pb-0 { padding-bottom: 0; }
.nova-pb-xs { padding-bottom: var(--spacing-xs); }
.nova-pb-sm { padding-bottom: var(--spacing-sm); }
.nova-pb-md { padding-bottom: var(--spacing-md); }
.nova-pb-lg { padding-bottom: var(--spacing-lg); }
.nova-pb-xl { padding-bottom: var(--spacing-xl); }
.nova-pb-2xl { padding-bottom: var(--spacing-2xl); }

/* تحسينات الأداء */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .nova-card::before {
    display: none !important;
  }
}

/* نظام التصميم المتجاوب المحسن */

/* Extra Small devices (phones, 320px and up) */
@media (min-width: 320px) {
  .nova-container {
    max-width: 100%;
    padding: 0 var(--spacing-md);
  }
}

/* Small devices (landscape phones, 480px and up) */
@media (min-width: 480px) {
  .nova-container {
    max-width: var(--container-sm);
    padding: 0 var(--spacing-lg);
  }

  .nova-grid-auto-sm {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .nova-container {
    max-width: var(--container-md);
  }

  .nova-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .nova-grid-auto-md {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .nova-container {
    max-width: var(--container-lg);
  }

  .nova-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .nova-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Extra large devices (large desktops, 1280px and up) */
@media (min-width: 1280px) {
  .nova-container {
    max-width: var(--container-xl);
  }

  .nova-grid-5 {
    grid-template-columns: repeat(5, 1fr);
  }

  .nova-grid-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 2XL devices (larger desktops, 1536px and up) */
@media (min-width: 1536px) {
  .nova-container {
    max-width: var(--container-2xl);
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 767px) {
  .nova-nav {
    display: none;
  }

  .nova-mobile-menu-btn {
    display: block;
  }

  .nova-container {
    margin-top: 100px;
    padding: 0 var(--spacing-md);
  }

  .nova-grid-2,
  .nova-grid-3,
  .nova-grid-4,
  .nova-grid-5,
  .nova-grid-6 {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .nova-btn {
    width: 100%;
    max-width: none;
  }

  .nova-text-3xl { font-size: var(--font-size-2xl); }
  .nova-text-4xl { font-size: var(--font-size-3xl); }
  .nova-text-5xl { font-size: var(--font-size-4xl); }
  .nova-text-6xl { font-size: var(--font-size-5xl); }
}

/* تحسينات للهواتف الصغيرة */
@media (max-width: 479px) {
  .nova-card {
    padding: var(--spacing-lg);
  }

  .nova-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: var(--btn-height-sm);
  }

  .nova-btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
    min-height: var(--btn-height-md);
  }

  .nova-text-2xl { font-size: var(--font-size-xl); }
  .nova-text-3xl { font-size: var(--font-size-2xl); }
  .nova-text-4xl { font-size: var(--font-size-3xl); }

  .nova-grid-gap-xl { gap: var(--spacing-lg); }
  .nova-grid-gap-2xl { gap: var(--spacing-xl); }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1920px) {
  .nova-container {
    max-width: 1800px;
  }

  .nova-text-6xl { font-size: 4.5rem; }
}

/* تحسينات للطباعة */
@media print {
  .nova-btn,
  .nova-nav,
  .nova-header {
    display: none !important;
  }

  .nova-card {
    border: 1px solid #000;
    box-shadow: none;
    page-break-inside: avoid;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
