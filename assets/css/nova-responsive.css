/* 
 * Nova Yemen - Advanced Responsive CSS
 * نوفا يمن - تحسينات متقدمة للتصميم المتجاوب
 * دعم شامل لجميع الأجهزة والشاشات
 */

/* Container Queries للتحكم الدقيق */
@container (min-width: 320px) {
  .nova-container-responsive {
    padding: var(--spacing-sm);
  }
}

@container (min-width: 768px) {
  .nova-container-responsive {
    padding: var(--spacing-lg);
  }
}

/* Clamp Functions للأحجام المتجاوبة */
.nova-text-responsive-xs { font-size: clamp(0.7rem, 2vw, 0.875rem); }
.nova-text-responsive-sm { font-size: clamp(0.8rem, 2.5vw, 1rem); }
.nova-text-responsive-md { font-size: clamp(1rem, 3vw, 1.25rem); }
.nova-text-responsive-lg { font-size: clamp(1.2rem, 4vw, 1.5rem); }
.nova-text-responsive-xl { font-size: clamp(1.5rem, 5vw, 2rem); }
.nova-text-responsive-2xl { font-size: clamp(2rem, 6vw, 3rem); }
.nova-text-responsive-3xl { font-size: clamp(2.5rem, 7vw, 4rem); }

/* Spacing متجاوب */
.nova-p-responsive { padding: clamp(1rem, 4vw, 2rem); }
.nova-m-responsive { margin: clamp(1rem, 4vw, 2rem); }
.nova-gap-responsive { gap: clamp(1rem, 3vw, 1.5rem); }

/* Grid متجاوب متقدم */
.nova-grid-responsive-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: clamp(1rem, 3vw, 2rem);
}

.nova-grid-responsive-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(280px, 100%), 1fr));
  gap: clamp(1rem, 2.5vw, 1.5rem);
}

/* Aspect Ratios */
.nova-aspect-square { aspect-ratio: 1 / 1; }
.nova-aspect-video { aspect-ratio: 16 / 9; }
.nova-aspect-photo { aspect-ratio: 4 / 3; }
.nova-aspect-golden { aspect-ratio: 1.618 / 1; }

/* Touch Optimization */
.nova-touch-target {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

.nova-touch-friendly {
  padding: var(--spacing-md);
  margin: var(--spacing-xs);
}

/* Scroll Behavior */
.nova-scroll-smooth {
  scroll-behavior: smooth;
  scroll-padding-top: 100px;
}

.nova-scroll-snap-x {
  scroll-snap-type: x mandatory;
  overflow-x: auto;
}

.nova-scroll-snap-y {
  scroll-snap-type: y mandatory;
  overflow-y: auto;
}

.nova-scroll-snap-item {
  scroll-snap-align: start;
}

/* Focus Management */
.nova-focus-visible:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

.nova-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-sm);
  z-index: 10000;
}

.nova-skip-link:focus {
  top: 6px;
}

/* Print Optimizations */
@media print {
  .nova-print-hidden {
    display: none !important;
  }
  
  .nova-print-visible {
    display: block !important;
  }
  
  .nova-card {
    border: 1px solid #000;
    box-shadow: none;
    page-break-inside: avoid;
  }
  
  .nova-btn {
    border: 1px solid #000;
    background: white !important;
    color: black !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .nova-card {
    border: 2px solid var(--text-primary);
  }
  
  .nova-btn {
    border: 2px solid currentColor;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .nova-motion-safe {
    animation: none !important;
    transition: none !important;
  }
  
  .nova-parallax {
    transform: none !important;
  }
}

/* Device-Specific Optimizations */

/* iPhone SE and small phones */
@media screen and (max-width: 375px) {
  .nova-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
  
  .nova-card {
    padding: var(--spacing-md);
  }
  
  .nova-text-3xl { font-size: var(--font-size-xl); }
  .nova-text-4xl { font-size: var(--font-size-2xl); }
}

/* Large phones */
@media screen and (min-width: 376px) and (max-width: 414px) {
  .nova-grid-responsive-cards {
    grid-template-columns: 1fr;
  }
}

/* Tablets Portrait */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .nova-grid-responsive-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .nova-container {
    max-width: 90%;
  }
}

/* Tablets Landscape */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .nova-grid-responsive-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Desktop */
@media screen and (min-width: 1025px) {
  .nova-grid-responsive-cards {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* Large Desktop */
@media screen and (min-width: 1440px) {
  .nova-container {
    max-width: 1400px;
  }
  
  .nova-grid-responsive-cards {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Ultra-wide screens */
@media screen and (min-width: 1920px) {
  .nova-container {
    max-width: 1800px;
  }
  
  .nova-grid-responsive-cards {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* 4K and beyond */
@media screen and (min-width: 2560px) {
  .nova-container {
    max-width: 2400px;
  }
  
  .nova-text-6xl { font-size: 5rem; }
}

/* Hover Support Detection */
@media (hover: hover) {
  .nova-hover-lift:hover {
    transform: translateY(-4px);
  }
  
  .nova-hover-scale:hover {
    transform: scale(1.05);
  }
  
  .nova-hover-glow:hover {
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.4);
  }
}

@media (hover: none) {
  .nova-touch-feedback:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

/* Pointer Precision */
@media (pointer: coarse) {
  .nova-btn {
    min-height: 48px;
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .nova-touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

@media (pointer: fine) {
  .nova-btn {
    min-height: 40px;
  }
  
  .nova-touch-target {
    min-height: 40px;
    min-width: 40px;
  }
}

/* Network-based optimizations */
@media (prefers-reduced-data) {
  .nova-bg-image {
    background-image: none;
  }
  
  .nova-video-bg {
    display: none;
  }
  
  .nova-animation-heavy {
    animation: none;
  }
}

/* Utility Classes for Responsive Design */
.nova-hidden-xs { display: none; }
.nova-hidden-sm { display: none; }
.nova-hidden-md { display: none; }
.nova-hidden-lg { display: none; }
.nova-hidden-xl { display: none; }

@media (min-width: 480px) {
  .nova-hidden-xs { display: initial; }
  .nova-visible-xs { display: none; }
}

@media (min-width: 768px) {
  .nova-hidden-sm { display: initial; }
  .nova-visible-sm { display: none; }
}

@media (min-width: 1024px) {
  .nova-hidden-md { display: initial; }
  .nova-visible-md { display: none; }
}

@media (min-width: 1280px) {
  .nova-hidden-lg { display: initial; }
  .nova-visible-lg { display: none; }
}

@media (min-width: 1536px) {
  .nova-hidden-xl { display: initial; }
  .nova-visible-xl { display: none; }
}

/* Performance Optimizations */
.nova-will-change-transform { will-change: transform; }
.nova-will-change-opacity { will-change: opacity; }
.nova-will-change-scroll { will-change: scroll-position; }

.nova-gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.nova-contain-layout { contain: layout; }
.nova-contain-style { contain: style; }
.nova-contain-paint { contain: paint; }
.nova-contain-size { contain: size; }
.nova-contain-strict { contain: strict; }
