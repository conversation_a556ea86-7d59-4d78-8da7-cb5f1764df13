/* 
 * Nova Yemen - Components CSS
 * نوفا يمن - مكونات التصميم
 * الإشعارات والمكونات التفاعلية
 */

/* Notifications */
.nova-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 500;
  z-index: 10000;
  transform: translateX(400px);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-width: 350px;
  word-wrap: break-word;
}

.nova-notification-show {
  transform: translateX(0);
}

.nova-notification-success {
  background: linear-gradient(135deg, var(--success-color), #218838);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.nova-notification-error {
  background: linear-gradient(135deg, var(--error-color), #c82333);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.nova-notification-warning {
  background: linear-gradient(135deg, var(--warning-color), #e0a800);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.nova-notification-info {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: 1px solid rgba(74, 144, 226, 0.3);
}

/* Loading Spinner */
.nova-spinner {
  border: 3px solid rgba(74, 144, 226, 0.1);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: nova-spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes nova-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Loading States */
.nova-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--primary-color);
  font-weight: 500;
}

.nova-loading-text {
  margin-top: var(--spacing-md);
  text-align: center;
}

/* Modal محسن */
.nova-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: nova-modal-fade-in 0.3s ease;
}

.nova-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes nova-modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.nova-modal-content {
  background: var(--bg-card);
  margin: 5% auto;
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  width: 90%;
  max-width: 600px;
  border: 2px solid rgba(74, 144, 226, 0.3);
  box-shadow: var(--shadow-heavy);
  position: relative;
  overflow: hidden;
}

.nova-modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(74, 144, 226, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.nova-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.nova-modal-title {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 700;
}

.nova-modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.nova-modal-close:hover {
  color: var(--error-color);
  background: rgba(220, 53, 69, 0.1);
}

.nova-modal-body {
  position: relative;
  z-index: 2;
}

/* تحسينات خاصة لنموذج التواصل */
#contactModal .nova-grid {
  margin-bottom: var(--spacing-lg);
}

#contactModal .nova-btn {
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}

#contactModal .nova-btn i {
  margin-left: var(--spacing-sm);
}

/* تأثيرات خاصة للأزرار في النموذج */
#contactModal .nova-btn-success:hover {
  background: linear-gradient(135deg, #25d366, #128c7e);
  transform: translateY(-2px) scale(1.02);
}

#contactModal .nova-btn-secondary:hover {
  background: linear-gradient(135deg, #0088cc, #005580);
  color: white;
}

#contactModal .nova-btn-warning:hover {
  background: linear-gradient(135deg, #e0a800, #d39e00);
  color: white;
}

/* زر التواصل العائم */
.nova-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #25d366, #128c7e);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nova-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
}

.nova-fab:active {
  transform: scale(0.95);
}

/* إخفاء الزر العائم على الشاشات الصغيرة جداً */
@media (max-width: 480px) {
  .nova-fab {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    bottom: 20px;
    right: 20px;
  }
}

/* Tabs */
.nova-tabs {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  border-bottom: 2px solid rgba(74, 144, 226, 0.1);
}

.nova-tab {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  cursor: pointer;
  transition: var(--transition-normal);
  font-weight: 500;
  position: relative;
}

.nova-tab::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.nova-tab:hover {
  color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
}

.nova-tab.active {
  color: var(--primary-color);
  background: rgba(74, 144, 226, 0.15);
}

.nova-tab.active::after {
  transform: scaleX(1);
}

.nova-tab-content {
  display: none;
}

.nova-tab-content.active {
  display: block;
}

/* Progress Bar */
.nova-progress {
  width: 100%;
  height: 8px;
  background: rgba(74, 144, 226, 0.1);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin: var(--spacing-md) 0;
}

.nova-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  position: relative;
}

.nova-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: nova-progress-shine 2s infinite;
}

@keyframes nova-progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Badges */
.nova-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nova-badge-primary {
  background: rgba(74, 144, 226, 0.2);
  color: var(--primary-color);
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.nova-badge-success {
  background: rgba(40, 167, 69, 0.2);
  color: var(--success-color);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.nova-badge-warning {
  background: rgba(255, 193, 7, 0.2);
  color: var(--warning-color);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.nova-badge-error {
  background: rgba(220, 53, 69, 0.2);
  color: var(--error-color);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Tooltips */
.nova-tooltip {
  position: relative;
  display: inline-block;
}

.nova-tooltip-text {
  visibility: hidden;
  width: 200px;
  background: var(--bg-card);
  color: var(--text-primary);
  text-align: center;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  border: 1px solid rgba(74, 144, 226, 0.2);
  box-shadow: var(--shadow-medium);
  font-size: 0.9rem;
}

.nova-tooltip-text::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--bg-card) transparent transparent transparent;
}

.nova-tooltip:hover .nova-tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Accordion */
.nova-accordion {
  border: 2px solid rgba(74, 144, 226, 0.2);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.nova-accordion-item {
  border-bottom: 1px solid rgba(74, 144, 226, 0.1);
}

.nova-accordion-item:last-child {
  border-bottom: none;
}

.nova-accordion-header {
  background: var(--bg-card);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nova-accordion-header:hover {
  background: rgba(74, 144, 226, 0.1);
}

.nova-accordion-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.1rem;
}

.nova-accordion-icon {
  color: var(--primary-color);
  transition: var(--transition-normal);
}

.nova-accordion-item.active .nova-accordion-icon {
  transform: rotate(180deg);
}

.nova-accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: rgba(255, 255, 255, 0.02);
}

.nova-accordion-content-inner {
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nova-notification {
    right: 10px;
    left: 10px;
    max-width: none;
    transform: translateY(-100px);
  }
  
  .nova-notification-show {
    transform: translateY(0);
  }
  
  .nova-modal-content {
    margin: 10% auto;
    padding: var(--spacing-lg);
    width: 95%;
  }
  
  .nova-tabs {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .nova-tab {
    border-radius: var(--radius-md);
  }
}

/* ===== مكونات محسنة للوضع الليلي/النهاري ===== */

/* نموذج التواصل المحسن */
.nova-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nova-modal.active {
  opacity: 1;
  visibility: visible;
}

.nova-modal-content {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  border: var(--border-glass);
  box-shadow: var(--shadow-heavy);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nova-modal.active .nova-modal-content {
  transform: scale(1) translateY(0);
}

.nova-modal-header {
  padding: var(--spacing-xl);
  border-bottom: var(--border-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nova-modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.nova-modal-close {
  background: var(--bg-tertiary);
  border: none;
  color: var(--text-primary);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.nova-modal-close:hover {
  background: var(--error-color);
  color: white;
  transform: scale(1.1);
}

.nova-modal-body {
  padding: var(--spacing-xl);
}

/* بطاقات الميزات المحسنة */
.nova-feature-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  border: var(--border-secondary);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nova-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(99, 102, 241, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.nova-feature-card:hover::before {
  left: 100%;
}

.nova-feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--primary-color);
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.05),
    var(--bg-card)
  );
}

.nova-feature-icon {
  font-size: clamp(3rem, 6vw, 4rem);
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  display: block;
  transition: all 0.3s ease;
  text-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.nova-feature-card:hover .nova-feature-icon {
  color: var(--secondary-color);
  transform: scale(1.1);
  text-shadow: 0 0 30px rgba(6, 182, 212, 0.5);
}

.nova-feature-title {
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  transition: color 0.3s ease;
}

.nova-feature-card:hover .nova-feature-title {
  color: var(--primary-color);
}

.nova-feature-description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  font-size: clamp(0.9rem, 2vw, 1rem);
}

/* الزر العائم للتواصل */
.nova-fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #25d366, #128c7e);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(37, 211, 102, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fabPulse 2s infinite;
}

.nova-fab:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 12px 40px rgba(37, 211, 102, 0.4);
  background: linear-gradient(135deg, #128c7e, #25d366);
}

.nova-fab:active {
  transform: scale(0.95);
}

@keyframes fabPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(37, 211, 102, 0.3);
  }
  50% {
    box-shadow: 0 8px 32px rgba(37, 211, 102, 0.6);
  }
}

/* بطاقات الخدمات المحسنة */
.nova-service-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  border: var(--border-secondary);
  padding: var(--spacing-2xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nova-service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(99, 102, 241, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.nova-service-card:hover::before {
  left: 100%;
}

.nova-service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--primary-color);
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.05),
    var(--bg-card)
  );
}

.nova-service-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.nova-service-title {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  transition: color 0.3s ease;
}

.nova-service-card:hover .nova-service-title {
  color: var(--secondary-color);
}

.nova-service-price {
  font-size: clamp(1rem, 2.5vw, 1.1rem);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.nova-service-features {
  list-style: none;
  margin-bottom: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.nova-service-features li {
  padding: var(--spacing-sm) 0;
  color: var(--text-primary);
  position: relative;
  padding-right: var(--spacing-xl);
  font-size: clamp(0.9rem, 2vw, 1rem);
  transition: color 0.3s ease;
}

.nova-service-features li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: var(--accent-color);
  font-weight: bold;
  font-size: 1.2em;
  text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.nova-service-card:hover .nova-service-features li {
  color: var(--text-secondary);
}

/* شريط التقدم المحسن */
.nova-progress {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin: var(--spacing-md) 0;
}

.nova-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.nova-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .nova-modal-content {
    width: 95%;
    margin: var(--spacing-md);
  }

  .nova-modal-header,
  .nova-modal-body {
    padding: var(--spacing-lg);
  }

  .nova-grid-2 {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .nova-fab {
    width: 56px;
    height: 56px;
    font-size: 20px;
    bottom: 20px;
    right: 20px;
  }

  .nova-feature-card,
  .nova-service-card {
    padding: var(--spacing-lg);
  }

  .nova-feature-icon {
    font-size: clamp(2.5rem, 8vw, 3rem);
  }
}

@media (max-width: 480px) {
  .nova-modal-content {
    width: 98%;
    margin: var(--spacing-sm);
    border-radius: var(--radius-lg);
  }

  .nova-modal-header,
  .nova-modal-body {
    padding: var(--spacing-md);
  }

  .nova-fab {
    width: 52px;
    height: 52px;
    font-size: 18px;
    bottom: 16px;
    right: 16px;
  }

  .nova-feature-card,
  .nova-service-card {
    padding: var(--spacing-md);
  }
}

/* تحسينات للطباعة */
@media print {
  .nova-fab,
  .nova-modal {
    display: none !important;
  }

  .nova-feature-card,
  .nova-service-card {
    border: 1px solid #000;
    box-shadow: none;
    page-break-inside: avoid;
  }
}

/* تحسينات للأجهزة ذات الأداء المنخفض */
@media (prefers-reduced-motion: reduce) {
  .nova-fab {
    animation: none !important;
  }

  .nova-progress-bar::after {
    animation: none !important;
  }

  .nova-feature-card::before,
  .nova-service-card::before {
    display: none !important;
  }
}
