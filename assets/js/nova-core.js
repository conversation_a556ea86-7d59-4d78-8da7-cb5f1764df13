/**
 * Nova Yemen - Core JavaScript
 * نوفا يمن - ملف الجافاسكريبت الأساسي
 * تحسينات الأداء والتفاعل
 */

class NovaCore {
  constructor() {
    this.isLowEndDevice = this.detectLowEndDevice();
    this.isSlowConnection = this.detectSlowConnection();
    this.prefersReducedMotion = this.detectReducedMotion();
    this.currentTheme = this.getStoredTheme() || this.getSystemTheme();
    this.mobileMenuOpen = false;

    this.init();
  }

  // الحصول على الوضع المحفوظ
  getStoredTheme() {
    return localStorage.getItem('nova-theme');
  }

  // الحصول على وضع النظام
  getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark';
  }

  // حفظ الوضع
  setStoredTheme(theme) {
    localStorage.setItem('nova-theme', theme);
  }

  // كشف الأجهزة الضعيفة
  detectLowEndDevice() {
    return navigator.hardwareConcurrency <= 2;
  }

  // كشف الاتصال البطيء
  detectSlowConnection() {
    return navigator.connection && navigator.connection.effectiveType && 
           (navigator.connection.effectiveType === 'slow-2g' || 
            navigator.connection.effectiveType === '2g');
  }

  // كشف تفضيل تقليل الحركة
  detectReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  // تطبيق الوضع الحالي
  applyTheme(theme = this.currentTheme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.currentTheme = theme;
    this.setStoredTheme(theme);

    // تحديث أيقونة الوضع
    this.updateThemeIcon();
  }

  // تبديل الوضع
  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);

    // تأثير بصري للتبديل
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);
  }

  // تحديث أيقونة الوضع
  updateThemeIcon() {
    const sunIcon = document.querySelector('.nova-theme-toggle-icon.sun');
    const moonIcon = document.querySelector('.nova-theme-toggle-icon.moon');

    if (sunIcon && moonIcon) {
      if (this.currentTheme === 'light') {
        sunIcon.style.opacity = '1';
        moonIcon.style.opacity = '0';
      } else {
        sunIcon.style.opacity = '0';
        moonIcon.style.opacity = '1';
      }
    }
  }

  // إعداد زر تبديل الوضع
  setupThemeToggle() {
    const themeToggle = document.querySelector('.nova-theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    // مراقبة تغيير وضع النظام
    window.matchMedia('(prefers-color-scheme: light)').addEventListener('change', (e) => {
      if (!this.getStoredTheme()) {
        this.applyTheme(e.matches ? 'light' : 'dark');
      }
    });
  }

  // تطبيق تحسينات الأداء
  applyPerformanceOptimizations() {
    if (this.isLowEndDevice || this.isSlowConnection || this.prefersReducedMotion) {
      document.body.classList.add('nova-low-performance');

      const style = document.createElement('style');
      style.textContent = `
        .nova-low-performance * {
          transition: none !important;
          animation: none !important;
          transform: none !important;
        }
        .nova-low-performance .nova-card::before,
        .nova-low-performance .nova-btn::before {
          display: none !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  // دالة throttle للأداء
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // دالة debounce للأداء
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // إعداد قائمة الهامبرغر
  setupMobileMenu() {
    const mobileMenuBtn = document.querySelector('.nova-mobile-menu-btn');
    const mobileOverlay = document.querySelector('.nova-mobile-overlay');
    const mobileMenu = document.querySelector('.nova-mobile-menu');
    const mobileMenuClose = document.querySelector('.nova-mobile-menu-close');

    if (!mobileMenuBtn) return;

    // فتح القائمة
    const openMenu = () => {
      this.mobileMenuOpen = true;
      mobileMenuBtn.classList.add('active');
      if (mobileOverlay) mobileOverlay.classList.add('active');
      if (mobileMenu) mobileMenu.classList.add('active');
      document.body.style.overflow = 'hidden';
    };

    // إغلاق القائمة
    const closeMenu = () => {
      this.mobileMenuOpen = false;
      mobileMenuBtn.classList.remove('active');
      if (mobileOverlay) mobileOverlay.classList.remove('active');
      if (mobileMenu) mobileMenu.classList.remove('active');
      document.body.style.overflow = '';
    };

    // أحداث فتح وإغلاق القائمة
    mobileMenuBtn.addEventListener('click', () => {
      this.mobileMenuOpen ? closeMenu() : openMenu();
    });

    if (mobileOverlay) {
      mobileOverlay.addEventListener('click', closeMenu);
    }

    if (mobileMenuClose) {
      mobileMenuClose.addEventListener('click', closeMenu);
    }

    // إغلاق القائمة عند الضغط على Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.mobileMenuOpen) {
        closeMenu();
      }
    });

    // إغلاق القائمة عند النقر على رابط
    const mobileNavLinks = document.querySelectorAll('.nova-mobile-menu-nav .nova-nav-link');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', closeMenu);
    });

    // إغلاق القائمة عند تغيير حجم الشاشة
    window.addEventListener('resize', () => {
      if (window.innerWidth > 768 && this.mobileMenuOpen) {
        closeMenu();
      }
    });
  }

  // تحديث header عند التمرير
  setupHeaderScroll() {
    const header = document.querySelector('.nova-header, header');
    if (!header) return;

    const updateHeader = this.throttle(() => {
      const scrolled = window.scrollY > 50;
      header.style.background = scrolled ?
        'var(--bg-card)' : 'var(--bg-glass)';
      header.style.boxShadow = scrolled ?
        'var(--shadow-heavy)' : 'var(--shadow-medium)';
    }, 16);

    window.addEventListener('scroll', updateHeader);
  }

  // Smooth scrolling للروابط الداخلية
  setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(anchor.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }

  // تحديث active navigation
  setupActiveNavigation() {
    const updateActiveNav = this.throttle(() => {
      const sections = document.querySelectorAll('section[id]');
      const navLinks = document.querySelectorAll('.nova-nav-link[href^="#"]');

      let current = '';
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        if (scrollY >= (sectionTop - 200)) {
          current = section.getAttribute('id');
        }
      });

      navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
          link.classList.add('active');
        }
      });
    }, 100);

    window.addEventListener('scroll', updateActiveNav);
  }

  // تحسين تحميل الصور (Lazy Loading)
  setupLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.remove('nova-lazy');
              imageObserver.unobserve(img);
            }
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }

  // تحسين الأداء عند تغيير حجم الشاشة
  setupResponsiveHandler() {
    const handleResize = this.throttle(() => {
      if (window.innerWidth <= 768) {
        document.body.classList.add('nova-mobile');
      } else {
        document.body.classList.remove('nova-mobile');
      }
    }, 250);

    window.addEventListener('resize', handleResize);
    handleResize(); // تشغيل مرة واحدة عند التحميل
  }

  // إعداد النماذج
  setupForms() {
    const forms = document.querySelectorAll('.nova-form');
    
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleFormSubmit(form);
      });
    });
  }

  // معالجة إرسال النماذج
  handleFormSubmit(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // يمكن تخصيص هذه الدالة حسب الحاجة
    console.log('Form submitted:', data);
    
    // إظهار رسالة نجاح
    this.showNotification('تم إرسال النموذج بنجاح!', 'success');
  }

  // إظهار الإشعارات
  showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `nova-notification nova-notification-${type}`;
    notification.textContent = message;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
      notification.classList.add('nova-notification-show');
    }, 100);
    
    // إخفاء الإشعار
    setTimeout(() => {
      notification.classList.remove('nova-notification-show');
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, duration);
  }

  // إعداد الأزرار التفاعلية
  setupInteractiveButtons() {
    const buttons = document.querySelectorAll('.nova-btn');

    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        // تأثير الضغط
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
          button.style.transform = '';
        }, 150);
      });
    });
  }

  // إعداد القائمة المحمولة
  setupMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu') || document.querySelector('.nav-links');

    if (!mobileMenuBtn || !navMenu) return;

    // تبديل القائمة
    mobileMenuBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleMobileMenu();
    });

    // إغلاق القائمة عند الضغط على رابط
    navMenu.querySelectorAll('a').forEach(link => {
      link.addEventListener('click', () => {
        this.closeMobileMenu();
      });
    });

    // إغلاق القائمة عند الضغط خارجها
    document.addEventListener('click', (e) => {
      if (!mobileMenuBtn.contains(e.target) && !navMenu.contains(e.target)) {
        this.closeMobileMenu();
      }
    });

    // إغلاق القائمة بمفتاح Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeMobileMenu();
      }
    });

    // إغلاق القائمة عند تغيير حجم الشاشة
    window.addEventListener('resize', () => {
      if (window.innerWidth > 768) {
        this.closeMobileMenu();
      }
    });
  }

  // تبديل القائمة المحمولة
  toggleMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu') || document.querySelector('.nav-links');

    if (!mobileMenuBtn || !navMenu) return;

    const isActive = navMenu.classList.contains('mobile-active');

    if (isActive) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  // فتح القائمة المحمولة
  openMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu') || document.querySelector('.nav-links');

    if (!mobileMenuBtn || !navMenu) return;

    navMenu.classList.add('mobile-active');
    mobileMenuBtn.classList.add('active');
    document.body.style.overflow = 'hidden';

    // تركيز على أول رابط
    const firstLink = navMenu.querySelector('a');
    if (firstLink) {
      setTimeout(() => firstLink.focus(), 100);
    }
  }

  // إغلاق القائمة المحمولة
  closeMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu') || document.querySelector('.nav-links');

    if (!mobileMenuBtn || !navMenu) return;

    navMenu.classList.remove('mobile-active');
    mobileMenuBtn.classList.remove('active');
    document.body.style.overflow = '';
  }

  // تحسين الخطوط
  optimizeFonts() {
    // تحميل الخطوط بشكل غير متزامن
    const fontLink = document.createElement('link');
    fontLink.rel = 'preload';
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap';
    fontLink.as = 'style';
    fontLink.onload = function() {
      this.onload = null;
      this.rel = 'stylesheet';
    };
    document.head.appendChild(fontLink);
  }

  // إعداد نموذج التواصل المنبثق
  setupContactModal() {
    // إنشاء نموذج التواصل إذا لم يكن موجوداً
    if (!document.getElementById('contactModal')) {
      this.createContactModal();
    }

    // ربط الأزرار بالنموذج (دعم للطرق المختلفة)
    document.querySelectorAll('[data-contact-modal]').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        this.openContactModal();
      });
    });

    // إغلاق النموذج عند الضغط خارجه
    document.addEventListener('click', (e) => {
      const modal = document.getElementById('contactModal');
      if (e.target === modal) {
        this.closeContactModal();
      }
    });

    // إغلاق النموذج بمفتاح Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && document.getElementById('contactModal')?.classList.contains('active')) {
        this.closeContactModal();
      }
    });
  }

  // إنشاء نموذج التواصل
  createContactModal() {
    const modalHTML = `
      <div id="contactModal" class="nova-modal">
        <div class="nova-modal-content">
          <div class="nova-modal-header">
            <h3 class="nova-modal-title">📞 تواصل معنا</h3>
            <button class="nova-modal-close" onclick="window.closeContactModal()">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="nova-modal-body">
            <div class="nova-grid nova-grid-2 nova-gap-lg">
              <a href="https://wa.me/967779600073?text=مرحباً، أريد الاستفسار عن خدمات نوفا يمن"
                 class="nova-btn nova-btn-success nova-btn-lg" target="_blank">
                <i class="fab fa-whatsapp"></i> واتساب
              </a>
              <a href="tel:+967779600073" class="nova-btn nova-btn-primary nova-btn-lg">
                <i class="fas fa-phone"></i> اتصال مباشر
              </a>
              <a href="https://t.me/novayemen" class="nova-btn nova-btn-secondary nova-btn-lg" target="_blank">
                <i class="fab fa-telegram"></i> تيليجرام
              </a>
              <a href="contact.html" class="nova-btn nova-btn-warning nova-btn-lg">
                <i class="fas fa-envelope"></i> نموذج التواصل
              </a>
            </div>
            <div class="nova-text-center nova-mt-lg">
              <p class="nova-text-secondary">أو يمكنك زيارة صفحة التواصل للمزيد من الخيارات</p>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // إضافة الزر العائم
    this.createFloatingActionButton();
  }

  // فتح نموذج التواصل
  openContactModal() {
    const modal = document.getElementById('contactModal');
    if (modal) {
      modal.classList.add('active');
      document.body.style.overflow = 'hidden';

      // تركيز على أول زر
      const firstButton = modal.querySelector('.nova-btn');
      if (firstButton) {
        setTimeout(() => firstButton.focus(), 100);
      }
    }
  }

  // إغلاق نموذج التواصل
  closeContactModal() {
    const modal = document.getElementById('contactModal');
    if (modal) {
      modal.classList.remove('active');
      document.body.style.overflow = '';
    }
  }

  // إنشاء زر التواصل العائم
  createFloatingActionButton() {
    // التحقق من عدم وجود الزر مسبقاً
    if (document.getElementById('novaFab')) return;

    const fabHTML = `
      <button id="novaFab" class="nova-fab" onclick="window.openContactModal()"
              title="تواصل معنا" aria-label="تواصل معنا">
        <i class="fab fa-whatsapp"></i>
      </button>
    `;

    document.body.insertAdjacentHTML('beforeend', fabHTML);

    // إضافة تأثير الظهور التدريجي
    const fab = document.getElementById('novaFab');
    fab.style.opacity = '0';
    fab.style.transform = 'scale(0)';

    setTimeout(() => {
      fab.style.transition = 'all 0.3s ease';
      fab.style.opacity = '1';
      fab.style.transform = 'scale(1)';
    }, 1000);
  }

  // تحسين الأداء العام
  optimizePerformance() {
    // تأجيل تحميل الموارد غير الضرورية
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.setupLazyLoading();
        this.optimizeFonts();
      });
    } else {
      setTimeout(() => {
        this.setupLazyLoading();
        this.optimizeFonts();
      }, 1000);
    }
  }

  // تهيئة النظام
  init() {
    // تطبيق الوضع المحفوظ أولاً
    this.applyTheme();

    // تطبيق تحسينات الأداء
    this.applyPerformanceOptimizations();

    // إعداد الوظائف الأساسية
    this.setupThemeToggle();
    this.setupMobileMenu();
    this.setupHeaderScroll();
    this.setupSmoothScrolling();
    this.setupActiveNavigation();
    this.setupResponsiveHandler();
    this.setupForms();
    this.setupInteractiveButtons();
    this.setupContactModal();

    // تحسينات الأداء
    this.optimizePerformance();

    console.log('🚀 Nova Yemen Core initialized successfully with theme:', this.currentTheme);
  }
}

// وظائف عامة للوصول إليها من أي مكان
window.openContactModal = function() {
  if (window.NovaCore) {
    window.NovaCore.openContactModal();
  }
};

window.closeContactModal = function() {
  if (window.NovaCore) {
    window.NovaCore.closeContactModal();
  }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  window.NovaCore = new NovaCore();
});

// إضافة دوال عامة للوصول إليها من HTML
window.openContactModal = function() {
  if (window.NovaCore) {
    window.NovaCore.openContactModal();
  }
};

window.closeContactModal = function() {
  if (window.NovaCore) {
    window.NovaCore.closeContactModal();
  }
};

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NovaCore;
}
