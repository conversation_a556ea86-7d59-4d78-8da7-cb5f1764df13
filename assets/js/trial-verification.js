/**
 * نظام التحقق للحسابات التجريبية
 * Nova Yemen Trial Verification System
 */

class TrialVerification {
    constructor() {
        this.currentService = '';
        this.countdownTimer = null;
        this.resendTimer = null;
        console.log('🔐 TrialVerification constructor called');
        this.init();
    }

    init() {
        console.log('🔐 Initializing Trial Verification System...');

        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupSystem();
            });
        } else {
            this.setupSystem();
        }
    }

    setupSystem() {
        console.log('🔧 Setting up verification system...');

        // ربط الأزرار بنظام التحقق
        this.bindTrialButtons();

        // إعداد مستمعي الأحداث
        this.setupEventListeners();

        console.log('✅ Trial Verification System initialized successfully');
    }

    // ربط أزرار التجربة بنظام التحقق
    bindTrialButtons() {
        // البحث عن أزرار التجربة بـ data-action
        const trialButtons = document.querySelectorAll('[data-action="trial"], [data-action="trial-plus"]');

        console.log(`🔍 Found ${trialButtons.length} trial buttons`);

        trialButtons.forEach(button => {
            // تحديد نوع الخدمة من data-action
            const action = button.getAttribute('data-action');
            const service = action === 'trial-plus' ? 'nova-plus' : 'nova';

            // إضافة مستمع الحدث الجديد
            button.addEventListener('click', (e) => {
                e.preventDefault();
                console.log(`🚀 Starting verification for service: ${service}`);
                this.startVerification(service);
            });

            console.log(`🔗 Bound trial button for service: ${service}`);
        });

        // البحث عن أزرار أخرى قد تحتوي على onclick
        const onclickButtons = document.querySelectorAll('[onclick*="proxy.php?path=create-trial"], [onclick*="proxy.php?path=create-line"]');

        onclickButtons.forEach(button => {
            // إزالة onclick الحالي
            button.removeAttribute('onclick');

            // تحديد نوع الخدمة
            const isNovaPlus = button.textContent.includes('المتقدمة') || button.textContent.includes('بلص') || button.textContent.includes('create-line');
            const service = isNovaPlus ? 'nova-plus' : 'nova';

            // إضافة مستمع الحدث الجديد
            button.addEventListener('click', (e) => {
                e.preventDefault();
                console.log(`🚀 Starting verification for service: ${service}`);
                this.startVerification(service);
            });

            console.log(`🔗 Bound onclick button for service: ${service}`);
        });
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مستمع لإدخال رقم الجوال (فقط أرقام)
        const phoneInput = document.getElementById('phoneNumber');
        if (phoneInput) {
            phoneInput.addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/[^0-9]/g, '');
            });
        }

        // مستمع لإدخال كود OTP (فقط أرقام)
        const otpInput = document.getElementById('otpCode');
        if (otpInput) {
            otpInput.addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/[^0-9]/g, '');
                
                // التحقق التلقائي عند إدخال 6 أرقام
                if (e.target.value.length === 6) {
                    setTimeout(() => this.verifyOTP(), 500);
                }
            });
        }

        // مستمع لإغلاق النموذج بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // مستمع لإغلاق النموذج بالضغط خارجه
        const modal = document.getElementById('verificationModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal();
                }
            });
        }
    }

    // بدء عملية التحقق
    startVerification(service) {
        console.log(`🚀 Starting verification for service: ${service}`);
        this.currentService = service;

        // التحقق من وجود النموذج
        const modal = document.getElementById('verificationModal');
        if (!modal) {
            console.error('❌ Verification modal not found!');
            alert('خطأ: نموذج التحقق غير موجود');
            return;
        }

        // تحديث عنوان النموذج
        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            const serviceName = service === 'nova-plus' ? 'نوفا بلص' : 'نوفا العادي';
            modalTitle.textContent = `تحقق للحصول على ${serviceName}`;
        }

        // إظهار النموذج
        this.showModal();

        // إعادة تعيين النموذج
        this.resetForm();

        console.log(`✅ Verification started for service: ${service}`);
    }

    // إظهار النموذج المنبثق
    showModal() {
        console.log('📱 Showing verification modal...');
        const modal = document.getElementById('verificationModal');

        if (!modal) {
            console.error('❌ Modal element not found!');
            return;
        }

        modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        console.log('✅ Modal displayed successfully');

        // تركيز على حقل رقم الجوال
        setTimeout(() => {
            const phoneInput = document.getElementById('phoneNumber');
            if (phoneInput) {
                phoneInput.focus();
                console.log('📱 Phone input focused');
            } else {
                console.error('❌ Phone input not found!');
            }
        }, 300);
    }

    // إخفاء النموذج المنبثق
    closeModal() {
        const modal = document.getElementById('verificationModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        
        // إيقاف العدادات
        this.stopTimers();
        
        // إعادة تعيين النموذج
        this.resetForm();
    }

    // إعادة تعيين النموذج
    resetForm() {
        // إظهار خطوة رقم الجوال
        this.showStep('phoneStep');
        
        // مسح الحقول
        document.getElementById('phoneNumber').value = '';
        document.getElementById('userName').value = '';
        document.getElementById('otpCode').value = '';
        
        // إعادة تعيين الأزرار
        this.resetButtons();
        
        // مسح الرسائل
        this.clearStatus();
    }

    // إظهار خطوة معينة
    showStep(stepId) {
        const steps = ['phoneStep', 'otpStep', 'successStep'];
        steps.forEach(step => {
            const element = document.getElementById(step);
            if (element) {
                element.classList.toggle('hidden', step !== stepId);
            }
        });
    }

    // إرسال كود OTP
    async sendOTP() {
        const phone = document.getElementById('phoneNumber').value.trim();
        const name = document.getElementById('userName').value.trim();
        
        // التحقق من البيانات
        if (!this.validatePhone(phone)) {
            this.showStatus('يرجى إدخال رقم جوال يمني صحيح', 'error');
            return;
        }
        
        if (!name) {
            this.showStatus('يرجى إدخال الاسم الكامل', 'error');
            return;
        }
        
        // تعطيل الزر وإظهار التحميل
        const sendBtn = document.getElementById('sendOtpBtn');
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        
        try {
            const response = await fetch('api/send-otp.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phone: phone,
                    name: name,
                    service: this.currentService
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showStatus(result.message, 'success');
                this.showStep('otpStep');
                this.startCountdown(result.data?.expires_in || 1800); // 30 دقيقة افتراضي
                
                // تركيز على حقل OTP
                setTimeout(() => {
                    document.getElementById('otpCode').focus();
                }, 300);
            } else {
                this.showStatus(result.message, 'error');
            }
        } catch (error) {
            console.error('Send OTP Error:', error);
            this.showStatus('حدث خطأ في الاتصال، يرجى المحاولة لاحقاً', 'error');
        } finally {
            // إعادة تعيين الزر
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال كود التحقق';
        }
    }

    // التحقق من كود OTP
    async verifyOTP() {
        const phone = document.getElementById('phoneNumber').value.trim();
        const otpCode = document.getElementById('otpCode').value.trim();
        
        if (!otpCode || otpCode.length !== 6) {
            this.showStatus('يرجى إدخال كود التحقق المكون من 6 أرقام', 'error');
            return;
        }
        
        // تعطيل الزر وإظهار التحميل
        const verifyBtn = document.getElementById('verifyOtpBtn');
        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';
        
        try {
            const response = await fetch('api/verify-otp.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phone: phone,
                    otp: otpCode,
                    service: this.currentService
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showStatus(result.message, 'success');
                this.showStep('successStep');
                
                // إنشاء الحساب التجريبي
                setTimeout(() => {
                    this.createTrialAccount(result.data);
                }, 2000);
            } else {
                this.showStatus(result.message, 'error');
            }
        } catch (error) {
            console.error('Verify OTP Error:', error);
            this.showStatus('حدث خطأ في التحقق، يرجى المحاولة لاحقاً', 'error');
        } finally {
            // إعادة تعيين الزر
            verifyBtn.disabled = false;
            verifyBtn.innerHTML = '<i class="fas fa-check"></i> تحقق من الكود';
        }
    }

    // إنشاء الحساب التجريبي
    async createTrialAccount(userData) {
        try {
            // استدعاء النظام الأصلي لإنشاء الحساب
            const proxyPath = this.currentService === 'nova-plus' ? 'create-line' : 'create-trial';
            
            // هنا يمكنك إضافة منطق إنشاء الحساب
            // أو استدعاء النظام الموجود مع البيانات المتحققة
            
            console.log('Creating trial account for verified user:', userData);
            
            // إغلاق النموذج بعد النجاح
            setTimeout(() => {
                this.closeModal();
                this.showSuccessMessage();
            }, 3000);
            
        } catch (error) {
            console.error('Create Trial Account Error:', error);
            this.showStatus('حدث خطأ في إنشاء الحساب، يرجى المحاولة لاحقاً', 'error');
        }
    }

    // إعادة إرسال كود OTP
    async resendOTP() {
        await this.sendOTP();
    }

    // التحقق من صحة رقم الجوال اليمني
    validatePhone(phone) {
        // إزالة المسافات والرموز
        phone = phone.replace(/[^0-9]/g, '');
        
        // التحقق من الطول والبداية
        if (phone.length === 9) {
            const validPrefixes = ['77', '78', '73', '70', '71', '72', '79'];
            const prefix = phone.substring(0, 2);
            return validPrefixes.includes(prefix);
        }
        
        return false;
    }

    // بدء العد التنازلي
    startCountdown(seconds) {
        this.stopTimers();
        
        const countdownElement = document.getElementById('countdown');
        const resendBtn = document.getElementById('resendOtpBtn');
        
        let timeLeft = seconds;
        
        this.countdownTimer = setInterval(() => {
            const minutes = Math.floor(timeLeft / 60);
            const secs = timeLeft % 60;
            
            countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                this.stopTimers();
                countdownElement.textContent = 'انتهت الصلاحية';
                resendBtn.disabled = false;
                resendBtn.textContent = 'إعادة إرسال';
            }
            
            timeLeft--;
        }, 1000);
        
        // تفعيل زر إعادة الإرسال بعد 10 دقائق
        setTimeout(() => {
            resendBtn.disabled = false;
        }, 600000); // 10 دقائق
    }

    // إيقاف العدادات
    stopTimers() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
        
        if (this.resendTimer) {
            clearTimeout(this.resendTimer);
            this.resendTimer = null;
        }
    }

    // إعادة تعيين الأزرار
    resetButtons() {
        const sendBtn = document.getElementById('sendOtpBtn');
        const verifyBtn = document.getElementById('verifyOtpBtn');
        const resendBtn = document.getElementById('resendOtpBtn');
        
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال كود التحقق';
        }
        
        if (verifyBtn) {
            verifyBtn.disabled = false;
            verifyBtn.innerHTML = '<i class="fas fa-check"></i> تحقق من الكود';
        }
        
        if (resendBtn) {
            resendBtn.disabled = true;
            resendBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة إرسال';
        }
    }

    // إظهار رسالة الحالة
    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('modalStatus');
        statusElement.textContent = message;
        statusElement.className = `status-message ${type}`;
        statusElement.style.display = 'block';
        
        // إخفاء الرسالة بعد 5 ثوان للرسائل الناجحة
        if (type === 'success') {
            setTimeout(() => {
                this.clearStatus();
            }, 5000);
        }
    }

    // مسح رسالة الحالة
    clearStatus() {
        const statusElement = document.getElementById('modalStatus');
        statusElement.textContent = '';
        statusElement.className = 'status-message';
        statusElement.style.display = 'none';
    }

    // إظهار رسالة النجاح
    showSuccessMessage() {
        // يمكنك إضافة إشعار نجاح هنا
        console.log('✅ Trial account created successfully!');
    }
}

// دوال عامة للاستدعاء من HTML
window.sendOTP = function() {
    if (window.trialVerification) {
        window.trialVerification.sendOTP();
    }
};

window.verifyOTP = function() {
    if (window.trialVerification) {
        window.trialVerification.verifyOTP();
    }
};

window.resendOTP = function() {
    if (window.trialVerification) {
        window.trialVerification.resendOTP();
    }
};

window.closeVerificationModal = function() {
    if (window.trialVerification) {
        window.trialVerification.closeModal();
    }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (!window.trialVerification) {
        console.log('🔐 Auto-initializing TrialVerification...');
        window.trialVerification = new TrialVerification();
    }
});

// تهيئة فورية إذا كان DOM جاهز
if (document.readyState === 'loading') {
    console.log('📱 DOM still loading, waiting...');
} else {
    console.log('📱 DOM ready, initializing immediately...');
    if (!window.trialVerification) {
        window.trialVerification = new TrialVerification();
    }
}

console.log('📱 Trial Verification Script Loaded');
