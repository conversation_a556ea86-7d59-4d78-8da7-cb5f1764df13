<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>من نحن | Nova Yemen - نوفا يمن</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

  <!-- Nova Theme CSS -->
  <link rel="stylesheet" href="assets/css/nova-theme.css">
  <link rel="stylesheet" href="assets/css/nova-components.css">

  <style>
    /* تخصيصات خاصة بصفحة من نحن */

      /* المسافات */
      --spacing-xs: 8px;
      --spacing-sm: 12px;
      --spacing-md: 16px;
      --spacing-lg: 24px;
      --spacing-xl: 32px;
      --spacing-2xl: 48px;

      /* نصف الأقطار */
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 16px;
      --radius-xl: 20px;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: "Cairo", "Cairo-Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      line-height: 1.6;
      overflow-x: hidden;
      font-display: swap;

      /* تحسينات الأداء */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeSpeed;
      contain: layout style;
    }

    /* Header المحسن */
    header {
      position: fixed;
      top: 0;
      width: 100%;
      background: rgba(26, 29, 35, 0.95);
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      padding: 15px 0;
      z-index: 1000;
      box-shadow: var(--shadow-medium);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-bottom: 1px solid rgba(74, 144, 226, 0.1);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--spacing-lg);
    }

    .logo {
      font-size: 2rem;
      font-weight: 900;
      color: var(--primary-color);
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .logo:hover {
      color: #6afcff;
      transform: scale(1.05);
    }

    .nav-links {
      display: flex;
      gap: var(--spacing-xl);
    }

    .nav-links a {
      color: var(--text-secondary);
      text-decoration: none;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      font-weight: 500;
      position: relative;
      border: 1px solid transparent;
    }

    .nav-links a::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }

    .nav-links a:hover {
      color: var(--primary-color);
      background: rgba(74, 144, 226, 0.1);
      transform: translateY(-2px);
      border-color: rgba(74, 144, 226, 0.2);
    }

    .nav-links a:hover::before {
      width: 100%;
    }

    .nav-links a.active {
      color: var(--primary-color);
      background: rgba(74, 144, 226, 0.15);
      border-color: rgba(74, 144, 226, 0.3);
    }

    .nav-links a.active::before {
      width: 100%;
    }

    /* Main Content المحسن */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 var(--spacing-lg);
      margin-top: 100px; /* مساحة للـ fixed header */
    }

    .page-header {
      text-align: center;
      margin-bottom: var(--spacing-2xl);
      padding: var(--spacing-2xl) 0;
      background: linear-gradient(135deg,
        rgba(45, 55, 72, 0.3),
        rgba(36, 40, 49, 0.5)
      );
      border-radius: var(--radius-xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .page-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 50%, rgba(74, 144, 226, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .page-header h1 {
      font-size: clamp(2.5rem, 6vw, 3.5rem);
      font-weight: 900;
      background: linear-gradient(135deg, var(--primary-color), #6afcff, var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: var(--spacing-lg);
      position: relative;
      z-index: 2;
    }

    .page-header::after {
      content: '';
      position: absolute;
      bottom: 30px;
      left: 50%;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      border-radius: 2px;
      transform: translateX(-50%);
    }

    .page-header p {
      font-size: clamp(1.1rem, 3vw, 1.3rem);
      color: var(--text-secondary);
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.8;
      position: relative;
      z-index: 2;
    }

    .content-section {
      background: var(--bg-card);
      padding: var(--spacing-2xl) var(--spacing-xl);
      border-radius: var(--radius-xl);
      margin-bottom: var(--spacing-xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .content-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .content-section:hover::before {
      left: 100%;
    }

    .content-section:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-heavy);
      border-color: rgba(74, 144, 226, 0.4);
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1),
        var(--bg-card)
      );
    }

    .content-section h2 {
      color: var(--primary-color);
      font-size: clamp(1.8rem, 4vw, 2.2rem);
      margin-bottom: var(--spacing-lg);
      font-weight: 700;
      position: relative;
      padding-bottom: var(--spacing-md);
      z-index: 2;
      transition: color 0.3s ease;
    }

    .content-section:hover h2 {
      color: #6afcff;
    }

    .content-section h2::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      border-radius: 2px;
    }

    .content-section p {
      font-size: clamp(1rem, 2.5vw, 1.1rem);
      line-height: 1.8;
      margin-bottom: var(--spacing-lg);
      color: var(--text-secondary);
      position: relative;
      z-index: 2;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: var(--spacing-xl);
      margin: var(--spacing-2xl) 0;
    }

    .feature-item {
      background: var(--bg-card);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      text-align: center;
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .feature-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .feature-item:hover::before {
      left: 100%;
    }

    .feature-item:hover {
      transform: translateY(-8px);
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-medium);
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1),
        var(--bg-card)
      );
    }

    .feature-item i {
      font-size: clamp(2.5rem, 6vw, 3rem);
      color: var(--primary-color);
      margin-bottom: var(--spacing-lg);
      display: block;
      transition: all 0.3s ease;
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
      position: relative;
      z-index: 2;
    }

    .feature-item:hover i {
      color: #6afcff;
      transform: scale(1.1);
      text-shadow: 0 0 30px rgba(106, 252, 255, 0.5);
    }

    .feature-item h3 {
      color: var(--text-primary);
      font-size: clamp(1.2rem, 3vw, 1.4rem);
      margin-bottom: var(--spacing-md);
      font-weight: 600;
      transition: color 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .feature-item:hover h3 {
      color: var(--primary-color);
    }

    .feature-item p {
      color: var(--text-secondary);
      font-size: clamp(0.9rem, 2vw, 1rem);
      line-height: 1.6;
      position: relative;
      z-index: 2;
    }

    .stats-section {
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.15),
        rgba(80, 200, 120, 0.1)
      );
      padding: var(--spacing-2xl) var(--spacing-xl);
      border-radius: var(--radius-xl);
      margin: var(--spacing-2xl) 0;
      text-align: center;
      border: 2px solid rgba(74, 144, 226, 0.2);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .stats-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 50%, rgba(74, 144, 226, 0.1) 0%, transparent 70%);
      pointer-events: none;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: var(--spacing-2xl);
      margin-top: var(--spacing-2xl);
      position: relative;
      z-index: 2;
    }

    .stat-item {
      text-align: center;
      padding: var(--spacing-lg);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s ease;
    }

    .stat-item:hover {
      transform: translateY(-5px);
      background: rgba(74, 144, 226, 0.1);
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-light);
    }

    .stat-number {
      font-size: clamp(2.5rem, 6vw, 3.5rem);
      font-weight: 900;
      color: var(--primary-color);
      display: block;
      margin-bottom: var(--spacing-sm);
      text-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
      transition: all 0.3s ease;
    }

    .stat-item:hover .stat-number {
      color: #6afcff;
      text-shadow: 0 0 30px rgba(106, 252, 255, 0.5);
      transform: scale(1.1);
    }

    .stat-label {
      font-size: clamp(1rem, 2.5vw, 1.1rem);
      color: var(--text-secondary);
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .stat-item:hover .stat-label {
      color: var(--text-primary);
    }

    .team-section {
      text-align: center;
      position: relative;
    }

    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-xl);
      margin-top: var(--spacing-2xl);
    }

    .team-member {
      background: var(--bg-card);
      padding: var(--spacing-xl);
      border-radius: var(--radius-lg);
      border: 2px solid rgba(74, 144, 226, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .team-member::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(74, 144, 226, 0.1),
        transparent
      );
      transition: left 0.6s ease;
    }

    .team-member:hover::before {
      left: 100%;
    }

    .team-member:hover {
      transform: translateY(-8px);
      border-color: rgba(74, 144, 226, 0.4);
      box-shadow: var(--shadow-medium);
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.1),
        var(--bg-card)
      );
    }

    .team-avatar {
      width: clamp(70px, 15vw, 90px);
      height: clamp(70px, 15vw, 90px);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      border-radius: 50%;
      margin: 0 auto var(--spacing-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: clamp(1.5rem, 4vw, 2rem);
      color: white;
      font-weight: bold;
      transition: all 0.3s ease;
      border: 3px solid rgba(74, 144, 226, 0.3);
      position: relative;
      z-index: 2;
    }

    .team-member:hover .team-avatar {
      background: linear-gradient(135deg, #6afcff, var(--accent-color));
      transform: scale(1.1);
      border-color: rgba(106, 252, 255, 0.5);
      box-shadow: 0 0 30px rgba(106, 252, 255, 0.3);
    }

    .team-name {
      color: var(--text-primary);
      font-size: clamp(1.1rem, 3vw, 1.3rem);
      font-weight: 600;
      margin-bottom: var(--spacing-sm);
      transition: color 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .team-member:hover .team-name {
      color: var(--primary-color);
    }

    .team-role {
      color: var(--primary-color);
      font-size: clamp(0.9rem, 2vw, 1rem);
      margin-bottom: var(--spacing-md);
      font-weight: 500;
      transition: color 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .team-member:hover .team-role {
      color: #6afcff;
    }

    .team-desc {
      color: var(--text-secondary);
      font-size: clamp(0.8rem, 2vw, 0.9rem);
      line-height: 1.6;
      position: relative;
      z-index: 2;
    }

    .contact-cta {
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.15),
        rgba(80, 200, 120, 0.1)
      );
      padding: var(--spacing-2xl);
      border-radius: var(--radius-xl);
      text-align: center;
      margin-top: var(--spacing-2xl);
      border: 2px solid rgba(74, 144, 226, 0.2);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .contact-cta::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 50%, rgba(74, 144, 226, 0.1) 0%, transparent 70%);
      pointer-events: none;
    }

    .contact-cta h2 {
      color: var(--primary-color);
      font-size: clamp(2rem, 5vw, 2.5rem);
      margin-bottom: var(--spacing-lg);
      position: relative;
      z-index: 2;
      background: linear-gradient(135deg, var(--primary-color), #6afcff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .contact-cta p {
      font-size: clamp(1.1rem, 3vw, 1.2rem);
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xl);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.8;
      position: relative;
      z-index: 2;
    }

    .cta-buttons {
      display: flex;
      gap: var(--spacing-lg);
      justify-content: center;
      flex-wrap: wrap;
      position: relative;
      z-index: 2;
    }

    .btn {
      display: inline-block;
      padding: var(--spacing-md) var(--spacing-xl);
      border-radius: var(--radius-lg);
      text-decoration: none;
      font-weight: 600;
      font-size: clamp(1rem, 2.5vw, 1.1rem);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      min-width: 180px;
      text-align: center;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .btn:hover::before {
      left: 100%;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      box-shadow: var(--shadow-light);
      border: none;
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
      transform: translateY(-3px);
      box-shadow: var(--shadow-medium);
    }

    .btn-secondary {
      background: transparent;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .btn-secondary:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-3px);
      box-shadow: var(--shadow-light);
    }





    /* Responsive Design المحسن */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        padding: 0 var(--spacing-md);
      }

      .nav-links {
        flex-direction: column;
        width: 100%;
        text-align: center;
        gap: var(--spacing-md);
      }

      .container {
        margin-top: 140px; /* مساحة أكبر للـ header المتوسع */
      }

      .content-section {
        padding: var(--spacing-xl) var(--spacing-lg);
      }

      .features-grid,
      .stats-grid,
      .team-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
      }

      .btn {
        width: 100%;
        max-width: 300px;
      }
    }

    @media (max-width: 480px) {
      .page-header {
        padding: var(--spacing-xl) var(--spacing-md);
      }

      .content-section {
        padding: var(--spacing-lg);
      }

      .stats-section,
      .contact-cta {
        padding: var(--spacing-lg);
      }

      .team-member,
      .feature-item {
        padding: var(--spacing-lg);
      }
    }

    /* تحسينات الأداء */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }

      .content-section::before,
      .feature-item::before,
      .team-member::before,
      .page-header::before,
      .stats-section::before,
      .contact-cta::before {
        display: none !important;
      }
    }

    /* تحسينات للطباعة */
    @media print {
      .page-header,
      .content-section,
      .stats-section {
        page-break-inside: avoid;
      }

      .nav-links {
        display: none;
      }

      body {
        background: white !important;
        color: black !important;
      }
    }
  </style>
</head>
<body>
  <!-- Header المحسن -->
  <header class="nova-header">
    <div class="nova-header-content">
      <a href="index.html" class="nova-logo">Nova Yemen</a>

      <nav class="nova-nav">
        <a href="index.html" class="nova-nav-link">الرئيسية</a>
        <a href="services.html" class="nova-nav-link">الخدمات</a>
        <a href="#" class="nova-nav-link active">من نحن</a>
      </nav>

      <div class="nova-header-controls">
        <!-- زر تبديل الوضع الليلي/النهاري -->
        <button class="nova-theme-toggle" title="تبديل الوضع الليلي/النهاري">
          <i class="fas fa-sun nova-theme-toggle-icon sun"></i>
          <i class="fas fa-moon nova-theme-toggle-icon moon"></i>
        </button>

        <!-- زر قائمة الهامبرغر -->
        <button class="nova-mobile-menu-btn">
          <div class="nova-hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>
  </header>

  <!-- القائمة المحمولة -->
  <div class="nova-mobile-overlay"></div>
  <div class="nova-mobile-menu">
    <div class="nova-mobile-menu-header">
      <div class="nova-logo">Nova Yemen</div>
      <button class="nova-mobile-menu-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <nav class="nova-mobile-menu-nav">
      <a href="index.html" class="nova-nav-link">الرئيسية</a>
      <a href="services.html" class="nova-nav-link">الخدمات</a>
      <a href="#" class="nova-nav-link active">من نحن</a>
    </nav>
  </div>

  <!-- Main Content -->
  <div class="container">
    <!-- Page Header -->
    <div class="page-header">
      <h1>من نحن</h1>
      <p>نحن نوفا يمن، رواد خدمات البث التلفزيوني في اليمن، نسعى لتقديم أفضل تجربة مشاهدة للمحتوى العربي والعالمي</p>
    </div>

    <!-- About Section -->
    <div class="content-section">
      <h2>قصتنا</h2>
      <p>
        تأسست نوفا يمن بهدف ثوري: تغيير طريقة مشاهدة التلفزيون في اليمن. بدأنا رحلتنا من فكرة بسيطة - توفير خدمة بث تلفزيوني عالية الجودة وبأسعار معقولة لكل بيت يمني.
      </p>
      <p>
        منذ انطلاقتنا، نمونا لنصبح واحدة من أكبر مقدمي خدمات IPTV في المنطقة، مع آلاف العملاء الراضين الذين يثقون بنا يومياً لتوفير أفضل تجربة ترفيهية.
      </p>
      <p>
        نحن لسنا مجرد شركة تقنية، بل شركاء في رحلة الترفيه اليومية لعائلاتكم. نفهم احتياجاتكم ونعمل باستمرار على تطوير خدماتنا لتلبية توقعاتكم المتزايدة.
      </p>
    </div>

    <!-- Mission & Vision -->
    <div class="content-section">
      <h2>رؤيتنا ورسالتنا</h2>
      <div class="features-grid">
        <div class="feature-item">
          <i class="fas fa-eye"></i>
          <h3>رؤيتنا</h3>
          <p>أن نكون الخيار الأول لخدمات البث التلفزيوني في اليمن والمنطقة العربية، ونقود التحول الرقمي في عالم الترفيه</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-bullseye"></i>
          <h3>رسالتنا</h3>
          <p>تقديم خدمات بث تلفزيوني عالية الجودة وموثوقة، مع دعم فني متميز وأسعار تنافسية تناسب جميع الأسر اليمنية</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-heart"></i>
          <h3>قيمنا</h3>
          <p>الجودة، الموثوقية، الابتكار، وخدمة العملاء المتميزة هي القيم التي تقود كل قرار نتخذه في نوفا يمن</p>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="stats-section">
      <h2 style="color: #6afcff; font-size: 2.5rem; margin-bottom: 20px;">أرقامنا تتحدث</h2>
      <p style="font-size: 1.2rem; color: #ddd; margin-bottom: 0;">إنجازاتنا في أرقام</p>
      
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-number">10,000+</span>
          <span class="stat-label">عميل راضٍ</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">5,000+</span>
          <span class="stat-label">قناة تلفزيونية</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">99.9%</span>
          <span class="stat-label">وقت تشغيل الخدمة</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">24/7</span>
          <span class="stat-label">دعم فني</span>
        </div>
      </div>
    </div>

    <!-- What Makes Us Different -->
    <div class="content-section">
      <h2>ما يميزنا</h2>
      <div class="features-grid">
        <div class="feature-item">
          <i class="fas fa-rocket"></i>
          <h3>تقنية متطورة</h3>
          <p>نستخدم أحدث التقنيات في البث والتشفير لضمان جودة عالية واستقرار في الخدمة</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-users"></i>
          <h3>فريق متخصص</h3>
          <p>فريق من المهندسين والفنيين المتخصصين يعمل على مدار الساعة لضمان أفضل خدمة</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-shield-alt"></i>
          <h3>أمان وموثوقية</h3>
          <p>نضمن حماية بياناتكم وخصوصيتكم مع خدمة مستقرة وآمنة 100%</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-dollar-sign"></i>
          <h3>أسعار عادلة</h3>
          <p>نقدم أفضل قيمة مقابل المال مع باقات مرنة تناسب جميع الميزانيات</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-headset"></i>
          <h3>دعم استثنائي</h3>
          <p>فريق دعم فني محترف متاح 24/7 لحل أي مشكلة أو استفسار</p>
        </div>
        <div class="feature-item">
          <i class="fas fa-sync-alt"></i>
          <h3>تحديثات مستمرة</h3>
          <p>نضيف محتوى جديد وقنوات حديثة باستمرار لنبقى في المقدمة دائماً</p>
        </div>
      </div>
    </div>

    <!-- Team Section -->
    <div class="content-section team-section">
      <h2>فريق العمل</h2>
      <p style="font-size: 1.1rem; color: #ddd; margin-bottom: 0;">الأشخاص الذين يجعلون نوفا يمن ممكناً</p>
      
      <div class="team-grid">
        <div class="team-member">
          <div class="team-avatar">و</div>
          <div class="team-name">وجدان السروري</div>
          <div class="team-role">المؤسس والمدير التنفيذي</div>
          <div class="team-desc">رائد في مجال التقنية والاتصالات، يقود الرؤية الاستراتيجية للشركة</div>
        </div>
        <div class="team-member">
          <div class="team-avatar">ف</div>
          <div class="team-name">فريق التطوير</div>
          <div class="team-role">المطورون والمهندسون</div>
          <div class="team-desc">فريق متخصص من المطورين والمهندسين يعمل على تطوير وصيانة الخدمات</div>
        </div>
        <div class="team-member">
          <div class="team-avatar">د</div>
          <div class="team-name">فريق الدعم الفني</div>
          <div class="team-role">خدمة العملاء</div>
          <div class="team-desc">فريق محترف متاح 24/7 لتقديم أفضل دعم فني وخدمة عملاء</div>
        </div>
      </div>
    </div>

    <!-- Contact CTA -->
    <div class="contact-cta">
      <h2>هل أنت مستعد للانضمام إلينا؟</h2>
      <p>ابدأ رحلتك مع نوفا يمن اليوم واستمتع بأفضل تجربة مشاهدة</p>
      <div class="nova-flex nova-justify-center nova-gap-lg nova-flex-wrap">
        <a href="services.html" class="nova-btn nova-btn-primary nova-btn-lg">
          <i class="fas fa-play"></i> ابدأ الآن
        </a>
        <button data-contact-modal class="nova-btn nova-btn-success nova-btn-lg">
          <i class="fas fa-phone"></i> تواصل معنا
        </button>
      </div>
    </div>
  </div>

  <script>
    // كشف الأجهزة الضعيفة للتحسين
    const isLowEndDevice = navigator.hardwareConcurrency <= 2;
    const isSlowConnection = navigator.connection && navigator.connection.effectiveType &&
                           (navigator.connection.effectiveType === 'slow-2g' ||
                            navigator.connection.effectiveType === '2g');

    // تطبيق تحسينات للأجهزة الضعيفة
    if (isLowEndDevice || isSlowConnection) {
      document.body.classList.add('low-performance-mode');

      const style = document.createElement('style');
      style.textContent = `
        .low-performance-mode * {
          transition: none !important;
          animation: none !important;
        }
        .low-performance-mode .content-section::before,
        .low-performance-mode .feature-item::before,
        .low-performance-mode .team-member::before,
        .low-performance-mode .page-header::before,
        .low-performance-mode .stats-section::before,
        .low-performance-mode .contact-cta::before {
          display: none !important;
        }
      `;
      document.head.appendChild(style);
    }

    // دالة throttle للأداء
    function throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    }

    // Header scroll effect محسن
    const updateHeader = throttle(function() {
      const header = document.querySelector('header');
      if (window.scrollY > 100) {
        header.style.background = 'rgba(26, 29, 35, 0.98)';
        header.style.borderBottomColor = 'rgba(74, 144, 226, 0.3)';
      } else {
        header.style.background = 'rgba(26, 29, 35, 0.95)';
        header.style.borderBottomColor = 'rgba(74, 144, 226, 0.1)';
      }
    }, 16);

    window.addEventListener('scroll', updateHeader);

    // Smooth scrolling للروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // تحسين الأداء عند تغيير حجم الشاشة
    const handleResize = throttle(function() {
      if (window.innerWidth <= 768) {
        document.body.classList.add('mobile-view');
      } else {
        document.body.classList.remove('mobile-view');
      }
    }, 250);

    window.addEventListener('resize', handleResize);
    handleResize(); // تشغيل مرة واحدة عند التحميل

    // تحسين تحميل الصور (Lazy Loading)
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            }
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }

    // تأثير العد للإحصائيات
    function animateCounters() {
      const counters = document.querySelectorAll('.stat-number');

      counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
          current += increment;
          if (current >= target) {
            counter.textContent = target.toLocaleString() + (counter.textContent.includes('+') ? '+' : '');
            clearInterval(timer);
          } else {
            counter.textContent = Math.floor(current).toLocaleString() + (counter.textContent.includes('+') ? '+' : '');
          }
        }, 20);
      });
    }

    // تشغيل العد عند ظهور قسم الإحصائيات
    if ('IntersectionObserver' in window) {
      const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            animateCounters();
            statsObserver.unobserve(entry.target);
          }
        });
      });

      const statsSection = document.querySelector('.stats-section');
      if (statsSection) {
        statsObserver.observe(statsSection);
      }
    }



    console.log('🚀 تم تحميل صفحة من نحن بنجاح مع التحسينات المتقدمة');
  </script>

  <!-- Nova Core JavaScript -->
  <script src="assets/js/nova-core.js"></script>
</body>
</html>
